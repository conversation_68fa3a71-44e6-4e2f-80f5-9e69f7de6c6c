package org.pkfrc.ehr.service.paie;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import org.pkfrc.core.entities.security.User;
import org.pkfrc.core.service.IBaseService;
import org.pkfrc.core.utilities.result.Result;
import org.pkfrc.core.utilities.result.ValidatorResult;
import org.pkfrc.ehr.entities.paie.EEtatPret;
import org.pkfrc.ehr.entities.paie.Pret;
import org.pkfrc.ehr.service.evaluation.document.ExportResult;
import org.springframework.web.multipart.MultipartFile;

import net.sf.jasperreports.engine.JRException;

public interface IPretService extends IBaseService<Long, Pret> {

	Result<Pret> create(User user, Pret record, MultipartFile file, String dir, String subDir,
			String exercice, boolean validate, String lang) throws Exception;

	List<Pret> getPretEncours(User user, String matricule);

	List<ValidatorResult> checkIfPossible(Pret record);

	ExportResult exportPretList(Long[] idList) throws JRException, IOException;

	Map<String, Object> getMontantEtNombreTotalPret(String matricule);

	Map<String, Object> getMontantEtNombreTotalPretEncours(String matricule);

	/*
	 * Map<String, Object> getMontantEtNombreTotalPretRembourse(String matricule);
	 * 
	 * Map<String, Object> getMontantEtNombreTotalPretSuspendu(String matricule);
	 */

	Map<String, Object> getMontantEtNombreTotalPretRestantEncours(String matricule);

	Map<String, Object> getGLobalPretByState(String matricule, EEtatPret etatPret);

	// Consolidated method to get all pret statistics in one call
	Map<String, Object> getAllPretStatisticsConsolidated(String matricule);
}
