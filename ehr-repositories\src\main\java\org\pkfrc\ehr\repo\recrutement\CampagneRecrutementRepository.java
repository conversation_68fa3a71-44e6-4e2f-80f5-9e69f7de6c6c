package org.pkfrc.ehr.repo.recrutement;

import java.util.List;
import java.util.Set;

import org.pkfrc.ehr.entities.recrutement.CampagneRecrutement;
import org.pkfrc.ehr.entities.recrutement.Candidat;
import org.pkfrc.ehr.entities.recrutement.EStatusCampagne;
import org.pkfrc.ehr.entities.recrutement.PvRecrutement;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface CampagneRecrutementRepository extends JpaRepository<CampagneRecrutement, Long> {
	List<PvRecrutement> findByPvRecrutement_Id(Long id);

	@Query("SELECT max(id) FROM CampagneRecrutement")
	Long getMaxID();

	// List<CampagneRecrutement> findByBesoin_Poste_CodeAndStatusEquals(String code,
	// EStatusCampagne status);

	List<CampagneRecrutement> findByBesoin_Fonction_CodeAndStatusEquals(String code, EStatusCampagne status);

	List<CampagneRecrutement> findByStatus(EStatusCampagne status);

	List<CampagneRecrutement> findByCandidats(Set<Candidat> candidats);

	// Optimized query to count active external campaigns
	@Query("SELECT COUNT(c) FROM CampagneRecrutement c " +
			"WHERE c.status = org.pkfrc.ehr.entities.recrutement.EStatusCampagne.Encours " +
			"AND c.besoin IS NOT NULL " +
			"AND (c.besoin.typeCampagne = org.pkfrc.ehr.entities.recrutement.ETypeBesoinCampagne.EXTERNE " +
			"OR c.besoin.typeCampagne = org.pkfrc.ehr.entities.recrutement.ETypeBesoinCampagne.MIXTE)")
	int countActiveExternalCampaigns();
}
