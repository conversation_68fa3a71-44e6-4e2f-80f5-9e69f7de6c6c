<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Connexion</title>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="/css/font-awesome/css/font-awesome.min.css">
</head>
<body>

<div class="modern-auth-wrapper">
    <div class="auth-container">
        <!-- Section Formulaire -->
        <div class="auth-form-section">
            <div class="form-content">
                <!-- Logo -->
                <div class="logo-section">
                    <img src="./img/afb/logo_2.png" alt="Logo" class="auth-logo">
                </div>

                <!-- Formulaire de connexion -->
                <div class="login-form-container">
                    <!-- En-tête -->
                    <div class="auth-header">
                        <h1 class="welcome-title">
                            {{ 'Bienvenue sur' | translate }} 
                            <span class="app-name">{{ vm.application.name }}</span>
                        </h1>
                        <p class="auth-subtitle">
                            <i class="fa fa-clock-o"></i>
                            <span>{{ vm.date | date:'dd/MM/yyyy HH:mm' }}</span>
                            <span class="separator">•</span>
                            <span>{{"Authentication" | translate}}</span>
                        </p>
                    </div>

                    <!-- Formulaire -->
                    <form name="form" ng-submit="vm.connexion()" class="modern-form">
                        <!-- Champ Matricule -->
                        <div class="form-group modern-form-group">
                            <label for="matricule" class="modern-label">
                                <i class="fa fa-user"></i>
                                {{ "Matricule" | translate }}
                            </label>
                         
                                <input type="text" 
                                       class="form-control" 
                                       id="matricule" 
                                       required
                                       ng-model="vm.record.login" 
                                       placeholder="{{ 'Votre Matricule' | translate }}">
                                <div class="input-focus-border"></div>
           
                        </div>

                        <!-- Champ Mot de passe -->
                        <div class="form-group modern-form-group mt-3">
                            <label for="password" class="modern-label">
                                <i class="fa fa-lock"></i>
                                {{ "Mot de passe" | translate }}
                            </label>
                                <input type="password" 
                                       class="form-control" 
                                       id="password" 
                                       required
                                       ng-model="vm.record.password" 
                                       placeholder="{{ 'Mot de passe' | translate }}">
                                
            
                        </div>

                        <!-- Lien mot de passe oublié -->
                        <div class="forgot-password-section">
                            <a ui-sref="simple.forgot_password" class="forgot-password-link">
                                <i class="fa fa-question-circle"></i>
                                {{ "Mot de passe oublié" | translate }}?
                            </a>
                        </div>

                        <!-- Bouton de connexion -->
                        <button type="submit" class="modern-login-btn" ng-disabled="form.$invalid">
                            <span class="btn-content">
                                <i class="fa fa-sign-in"></i>
                                <span>{{ "Connexion" | translate }}</span>
                            </span>
                        </button>
                    </form>

                    <!-- Footer -->
                    <div class="auth-footer">
                        <p class="security-info">
                            <i class="fa fa-shield"></i>
                            Connexion sécurisée SSL
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Section Image de fond -->
        <div class="auth-background-section">
            <div class="background-overlay"></div>
        </div>
    </div>
</div>

<style>

/* Variables CSS */
:root {
    --primary-red: #DD3333;
    --primary-red-hover: #bb2222;
    --primary-red-light: rgba(221, 51, 51, 0.1);
    --gray-50: #f8f9fa;
    --gray-100: #e9ecef;
    --gray-200: #dee2e6;
    --gray-300: #ced4da;
    --gray-400: #adb5bd;
    --gray-500: #6c757d;
    --gray-600: #495057;
    --gray-700: #343a40;
    --gray-800: #212529;
    --gray-900: #1a1a1a;
    --white: #ffffff;
    --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
    --border-radius: 8px;
    --border-radius-lg: 12px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Reset et base */
* {
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: var(--gray-800);
    background: var(--gray-50);
    margin: 0;
    padding: 0;
}

/* Container principal */
.modern-auth-wrapper {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
}

.auth-container {
    width: 100%;
    max-width: 1200px;
    background: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    display: grid;
    grid-template-columns: 1fr 1.2fr;
    min-height: 600px;
}

/* Section formulaire */
.auth-form-section {
    padding: 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    background: var(--white);
}

.form-content {
    width: 100%;
    max-width: 400px;
    margin: 0 auto;
}

/* Logo */
.logo-section {
    text-align: center;
    margin-bottom: 32px;
}

.auth-logo {
    max-width: 180px;
    height: auto;
    object-fit: contain;
}

/* Container du formulaire */
.login-form-container {
    width: 100%;
}

/* En-tête */
.auth-header {
    text-align: center;
    margin-bottom: 32px;
}

.welcome-title {
    font-size: 24px;
    font-weight: 700;
    color: var(--gray-900);
    margin: 0 0 8px 0;
    line-height: 1.3;
}

.app-name {
    color: var(--primary-red);
    font-weight: 800;
}

.auth-subtitle {
    color: var(--gray-600);
    font-size: 14px;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.separator {
    color: var(--gray-400);
}

/* Formulaire moderne */
.modern-form {
    width: 100%;
}

.modern-form-group {
    margin-bottom: 24px;
}

.modern-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: 8px;
    font-size: 14px;
}

.modern-label i {
    color: var(--primary-red);
    width: 16px;
    text-align: center;
}


.form-control::placeholder {
    color: var(--gray-400) !important;
}


.password-toggle {
    position: absolute !important;
    right: 16px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    background: none !important;
    border: none !important;
    color: var(--gray-500) !important;
    cursor: pointer !important;
    padding: 8px !important;
    border-radius: 4px !important;
    transition: var(--transition) !important;
    z-index: 10 !important;
    width: 32px !important;
    height: 32px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.password-toggle:hover {
    color: var(--primary-red) !important;
    background: var(--primary-red-light) !important;
}

.password-toggle i {
    font-size: 16px !important;
}



/* Lien mot de passe oublié */
.forgot-password-section {
    text-align: right;
    margin-top: 24px;
}

.forgot-password-link {
    color: var(--primary-red) !important;
    text-decoration: none !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    display: inline-flex !important;
    align-items: center !important;
    gap: 4px !important;
    transition: var(--transition) !important;
}

.forgot-password-link:hover {
    color: var(--primary-red-hover) !important;
    text-decoration: underline !important;
}

.forgot-password-link i {
    font-size: 12px !important;
}

/* Bouton de connexion moderne */
.modern-login-btn {
    width: 100% !important;
    padding: 16px 24px !important;
    background: linear-gradient(135deg, var(--primary-red) 0%, var(--primary-red-hover) 100%) !important;
    color: var(--white) !important;
    border: none !important;
    border-radius: var(--border-radius) !important;
    font-size: 16px !important;
    font-weight: 600 !important;
    cursor: pointer !important;
    transition: var(--transition) !important;
    position: relative !important;
    overflow: hidden !important;
    box-shadow: var(--shadow) !important;
    z-index: 1 !important;
}

.modern-login-btn:hover:not(:disabled) {
    transform: translateY(-2px) !important;
    box-shadow: var(--shadow-lg) !important;
    background: linear-gradient(135deg, var(--primary-red-hover) 0%, #aa1111 100%) !important;
}

.modern-login-btn:active {
    transform: translateY(0) !important;
}

.modern-login-btn:disabled {
    background: var(--gray-300) !important;
    cursor: not-allowed !important;
    transform: none !important;
    box-shadow: var(--shadow-sm) !important;
}

.btn-content {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 8px !important;
}

.btn-content i {
    font-size: 16px !important;
}

.btn-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

/* Footer */
.auth-footer {
    margin-top: 32px;
    text-align: center;
}

.security-info {
    color: var(--gray-500);
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    margin: 0;
}

.security-info i {
    color: var(--primary-red);
    font-size: 14px;
}

/* Section arrière-plan avec image */
.auth-background-section {
    background-image: url('./img/bg-login.jpeg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.background-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(221, 51, 51, 0.6) 0%, rgba(187, 34, 34, 0.8) 100%);
}

/* Responsive Design */
@media (max-width: 968px) {
    .auth-container {
        grid-template-columns: 1fr;
        max-width: 500px;
    }
    
    .auth-background-section {
        display: none;
    }
    
    .auth-form-section {
        padding: 32px 24px;
    }
}

@media (max-width: 576px) {
    .modern-auth-wrapper {
        padding: 16px;
    }
    
    .auth-form-section {
        padding: 24px 20px;
    }
    
    .welcome-title {
        font-size: 20px;
    }
    
    .form-control {
        padding: 14px 16px !important;
        font-size: 16px !important;
    }
    
    .modern-login-btn {
        padding: 14px 20px !important;
    }
    
    .auth-subtitle {
        flex-direction: column;
        gap: 4px;
    }
    
    .separator {
        display: none;
    }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.form-content {
    animation: fadeInUp 0.6s ease-out;
}

.modern-form-group {
    animation: fadeInUp 0.6s ease-out;
    animation-fill-mode: both;
}

.modern-form-group:nth-child(1) { animation-delay: 0.1s; }
.modern-form-group:nth-child(2) { animation-delay: 0.2s; }
.modern-form-group:nth-child(3) { animation-delay: 0.3s; }
.modern-form-group:nth-child(4) { animation-delay: 0.4s; }

.modern-login-btn {
    animation: fadeInUp 0.6s ease-out 0.5s both;
}

/* États de validation */
.form-control.ng-invalid.ng-dirty {
    border-color: #dc3545 !important;
}

.form-control.ng-valid.ng-dirty {
    border-color: #28a745 !important;
}

/* Focus visible pour l'accessibilité */
.modern-login-btn:focus-visible,
.password-toggle:focus-visible,
.forgot-password-link:focus-visible {
    outline: 2px solid var(--primary-red) !important;
    outline-offset: 2px !important;
}
</style>

</body>
</html>
