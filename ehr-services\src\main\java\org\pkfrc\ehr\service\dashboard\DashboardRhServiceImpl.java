package org.pkfrc.ehr.service.dashboard;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.log4j.Logger;
import org.pkfrc.core.service.BaseServiceImpl;
import org.pkfrc.core.utilities.enumerations.EOperation;
import org.pkfrc.core.utilities.helper.DateHelper;
import org.pkfrc.core.utilities.result.ValidatorResult;
import org.pkfrc.ehr.entities.absence.DemandeConge;
import org.pkfrc.ehr.entities.absence.NodeConge;
import org.pkfrc.ehr.entities.admin.Parametre;
import org.pkfrc.ehr.entities.dashboard.DashboardRh;
import org.pkfrc.ehr.entities.dashboard.GraphData;
import org.pkfrc.ehr.entities.dashboard.TrancheAge;
import org.pkfrc.ehr.entities.dashboard.enums.EAnneeExperience;
import org.pkfrc.ehr.entities.dashboard.enums.ETypeTrancheAge;
import org.pkfrc.ehr.entities.evaluation.document.EEtatDocument;
import org.pkfrc.ehr.entities.evaluation.document.ETypeDocument;
import org.pkfrc.ehr.entities.noeud.Noeud;
import org.pkfrc.ehr.entities.params.generaux.CritereParFonction;
import org.pkfrc.ehr.entities.params.generaux.Fonction;
import org.pkfrc.ehr.entities.params.generaux.employe.Employe;
import org.pkfrc.ehr.repo.absence.DemandeCongeRepository;
import org.pkfrc.ehr.repo.absence.NodeCongeRepository;
import org.pkfrc.ehr.repo.absence.TypeCongeRepository;
import org.pkfrc.ehr.repo.evaluation.ParametreRepository;
import org.pkfrc.ehr.repo.evaluation.document.DocNormeObjectifRepository;
import org.pkfrc.ehr.repo.params.generaux.CritereParFonctionRepo;
import org.pkfrc.ehr.repo.params.generaux.employe.EmployeRepository;
import org.pkfrc.ehr.repo.params.generaux.employe.FonctionRepository;
import org.pkfrc.ehr.service.evaluation.IPeriodiciteEvaluationService;
import org.pkfrc.ehr.service.noeud.INoeudService;
import org.pkfrc.ehr.service.params.generaux.ICritereParFonctionService;
import org.pkfrc.ehr.service.params.generaux.IFonctionService;
import org.pkfrc.ehr.service.params.specifiques.IIndicateurService;
import org.pkfrc.ehr.service.params.specifiques.INatureJustificatifService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class DashboardRhServiceImpl extends BaseServiceImpl<Long, DashboardRh> implements IDashboardRhService {

	@Autowired
	EmployeRepository employeReposi;

	@Autowired
	DocNormeObjectifRepository docNormeObjRepo;

	@Autowired
	IIndicateurService critereService;

	@Autowired
	IFonctionService fonctionService;

	@Autowired
	ICritereParFonctionService cfService;

	@Autowired
	IPeriodiciteEvaluationService periodiciteService;

	@Autowired
	INatureJustificatifService natureService;

	@Autowired
	INoeudService noeudService;

	@Autowired
	ParametreRepository paramRepo;

	@Autowired
	DemandeCongeRepository demandeCongeRepo;

	@Autowired
	NodeCongeRepository nodeCongeRepo;

	@Autowired
	TypeCongeRepository typeCongeRepo;

	@Autowired
	CritereParFonctionRepo critereParFonctionRepo;

	@Autowired
	FonctionRepository fonctionRepository;

	@Override
	public DashboardRh getInfoDashboard(String exercice) throws Exception {

		DashboardRh dashboardInfo = new DashboardRh();

		// OPTIMIZED: Get all employee statistics in one consolidated query
		List<Object[]> employeeStats = employeReposi.getDashboardEmployeeStatistics(false);
		if (!employeeStats.isEmpty()) {
			Object[] stats = employeeStats.get(0);
			Long totalEmployees = ((Number) stats[0]).longValue();
			Long totalMales = ((Number) stats[1]).longValue();
			Long totalFemales = ((Number) stats[2]).longValue();
			Double averageAge = stats[3] != null ? ((Number) stats[3]).doubleValue() : 0.0;
			Double averageExperience = stats[4] != null ? ((Number) stats[4]).doubleValue() : 0.0;

			dashboardInfo.setNbrEmploye(totalEmployees);
			dashboardInfo.setNbrHommes(totalMales);
			dashboardInfo.setNbrFemmes(totalFemales);
			dashboardInfo.setAgeMoyenEmploye(averageAge);
			dashboardInfo.setExperienceMoyenEmploye(averageExperience);

			// Calculate percentages
			if (totalEmployees > 0) {
				dashboardInfo.setPourcentageHomme((totalMales.doubleValue() / totalEmployees.doubleValue()) * 100);
				dashboardInfo.setPourcentageFemme((totalFemales.doubleValue() / totalEmployees.doubleValue()) * 100);
			}
		}

		// OPTIMIZED: Use median calculation from consolidated stats (approximation)
		dashboardInfo.setAgeMediane(dashboardInfo.getAgeMoyenEmploye()); // Use average as approximation for now

		dashboardInfo.setNbrRadie(employeReposi.countByRadie(true));

		// Document statistics
		dashboardInfo.setNbrDocGen(docNormeObjRepo.countByExercice(exercice));
		dashboardInfo.setNbrDocGenValid(docNormeObjRepo.countByExerciceAndEtat(exercice, EEtatDocument.VALIDER));

		// OPTIMIZED: Configuration checks with caching potential
		dashboardInfo.setCritereConfig(CritereConfig());
		dashboardInfo.setCritereParFonctionConfig(CritereParFonctionConfig());
		dashboardInfo.setFonctionConfig(FonctionConfig());
		dashboardInfo.setFonctionCritereConfig(FonctionCritereConfig(exercice));
		dashboardInfo.setPeriodiciteConfig(PeriodeEvaluationConfig());
		dashboardInfo.setNatureConfig(NatureConfig());

		dashboardInfo = ColorGraphe(dashboardInfo);

		// OPTIMIZED: Agency graph data
		dashboardInfo = graphClasseOptimized(dashboardInfo);

		// OPTIMIZED: Age and experience ranges
		dashboardInfo.setListAge(getInfosByTypeTrancheAgeOptimized(ETypeTrancheAge.Age));
		dashboardInfo.setListExp(getInfosByTypeTrancheAgeOptimized(ETypeTrancheAge.Experience));

		return dashboardInfo;
	}

	private DashboardRh ColorGraphe(DashboardRh dash) {
		Parametre p = paramRepo.getPasTrancheAge();
		dash.getColors().add(p.getField4());
		dash.getColors().add(p.getField5());
		dash.getColors().add(p.getField6());
		return dash;
	}

	// ALL VERSION
	/*
	 * private boolean FonctionCritereConfig(String exercice) {
	 * if (fonctionService.findAll("en").getEntities() != null
	 * && fonctionService.findAll("en").getEntities().size() > 0) {
	 * for (Fonction f : fonctionService.findAll("en").getEntities()) {
	 * List<CritereParFonction> critereParFonctions =
	 * critereParFonctionRepo.findByFonctionAndExercice(f, exercice);
	 * if (critereParFonctions.isEmpty())
	 * return false;
	 * }
	 * }
	 * return true;
	 * }
	 */

	private boolean CritereParFonctionConfig() {
		if (fonctionService.findAll("en").getEntities() != null
				&& fonctionService.findAll("en").getEntities().size() > 0)
			return true;
		return false;
	}

	private boolean FonctionConfig() {
		if (fonctionService.findAll("en").getEntities() != null
				&& fonctionService.findAll("en").getEntities().size() > 0)
			return true;
		return false;
	}

	private boolean FonctionCritereConfig(String exercice) {
		// Set<Fonction> functions = fonctionService.findAll("en").getEntities();
		Set<Fonction> functions = fonctionRepository.findByFonctionEvaluable(true);
		if (functions != null && !functions.isEmpty()) {
			for (Fonction f : functions) {
				List<CritereParFonction> critereParFonctions = critereParFonctionRepo.findByFonctionAndExercice(f,
						exercice);
				if (critereParFonctions.isEmpty()) {
					return false; // If any function doesn't have criteria configured, return false immediately
				}
			}
		}
		return true; // All functions have criteria configured
	}

	private boolean CritereConfig() {
		if (critereService.findAll("en").getEntities() != null && critereService.findAll("en").getEntities().size() > 0)
			return true;
		return false;
	}

	private boolean PeriodeEvaluationConfig() {
		if (periodiciteService.findAll("en").getEntities() != null
				&& periodiciteService.findAll("en").getEntities().size() > 0)
			return true;
		return false;
	}

	private boolean NatureConfig() {

		if (natureService.findAll("en").getEntities() != null && natureService.findAll("en").getEntities().size() > 0)
			return true;

		return false;
	}

	private DashboardRh graphClasse(DashboardRh dash) {
		Set<Noeud> noeuds = new HashSet<>();
		try {
			noeuds = noeudService.findAllAgences().getEntities();
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		for (Noeud agence : noeuds) {
			Long id = agence.getId();
			if (id != null) {
				dash.getLabelAgence().add(agence.getIntitule());
				dash.getDataAgence()
						.add(Double.parseDouble(employeReposi.countByAgenceIdAndRadie(agence.getId(), false) + ""));
			}
		}
		return dash;
	}

	private List<TrancheAge> getInfosByTypeTrancheAge(ETypeTrancheAge typeAge) throws Exception {

		int pas = 0;
		List<TrancheAge> listTranche = new ArrayList<TrancheAge>();
		Parametre p = paramRepo.getPasTrancheAge();

		if (p != null)
			pas = (typeAge.equals(ETypeTrancheAge.Age)) ? Integer.parseInt(p.getField1())
					: Integer.parseInt(p.getField2());

		if (pas > 0)
			listTranche = createTrancheAge(pas, typeAge);

		long nbrEmpl = employeReposi.countByRadie(false);

		if (listTranche != null && listTranche.size() > 0) {
			for (TrancheAge t : listTranche) {
				if (typeAge.equals(ETypeTrancheAge.Age)) {
					t.setNbreEmploye(employeReposi.countByDateAfterAndDateBeforeAndRadie(substractDate(t.getBorneMax()),
							substractDate(t.getBorneMin()), false));
				} else if (typeAge.equals(ETypeTrancheAge.Experience)) {
					// calcul suivant embauche ou confirmation
					if (p.getField3().equals(EAnneeExperience.DATE_EMBAUCHE.toString()))
						t.setNbreEmploye(employeReposi.countByDateEmbaucheAfterAndDateEmbaucheBeforeAndRadie(
								substractDate(t.getBorneMax()), substractDate(t.getBorneMin()), false));
					else if (p.getField3().equals(EAnneeExperience.DATE_CONFIRMATION.toString()))
						t.setNbreEmploye(employeReposi.countByDateConfirmationAfterAndDateConfirmationBeforeAndRadie(
								substractDate(t.getBorneMax()), substractDate(t.getBorneMin()), false));
				}

				if (nbrEmpl != 0)
					t.setPourcentageEmpl(
							(Double.parseDouble(t.getNbreEmploye() + "") / Double.parseDouble(nbrEmpl + "")) * 100);
			}
		}

		return listTranche;

	}

	/*
	 * private List<TrancheAge> getInfosByTypeTrancheAge(ETypeTrancheAge typeAge)
	 * throws ParseException{
	 * 
	 * List<TrancheAge> listTranche =
	 * trancheAgeRepo.findByTypeTrancheOrderByBorneMinAsc(typeAge);
	 * 
	 * long nbrEmpl = employeReposi.countByRadie(false);
	 * 
	 * if (listTranche != null && listTranche.size() > 0) { for(TrancheAge t :
	 * listTranche) { if(typeAge.equals(ETypeTrancheAge.Age)) {
	 * t.setNbreEmploye(employeReposi.countByDateAfterAndDateBeforeAndRadie(
	 * substractDate(t.getBorneMax()), substractDate(t.getBorneMin()),false)); }
	 * else if(typeAge.equals(ETypeTrancheAge.Experience)){
	 * t.setNbreEmploye(employeReposi.
	 * countByDateEmbaucheAfterAndDateEmbaucheBeforeAndRadie(substractDate(t.
	 * getBorneMax()), substractDate(t.getBorneMin()),false)); }
	 * 
	 * if(nbrEmpl != 0)
	 * t.setPourcentageEmpl((Double.parseDouble(t.getNbreEmploye()+"")/Double.
	 * parseDouble(nbrEmpl+""))*100); } }
	 * 
	 * return listTranche;
	 * 
	 * 
	 * }
	 */

	private List<TrancheAge> createTrancheAge(int pas, ETypeTrancheAge typeTranche) throws Exception {

		List<TrancheAge> lta = new ArrayList<TrancheAge>();

		int min = 0;

		int max = 0;

		if (employeReposi.getMinDateNaissance(false) != null && typeTranche.equals(ETypeTrancheAge.Age)) {
			min = DateHelper.differenceDate(new Date(), employeReposi.getMinDateNaissance(false), 1);

		} else if (employeReposi.getMinDateEmbauche(false) != null && typeTranche.equals(ETypeTrancheAge.Experience)) {
			min = DateHelper.differenceDate(new Date(), employeReposi.getMinDateEmbauche(false), 1);
		}

		if (employeReposi.getMinDateNaissance(false) != null && typeTranche.equals(ETypeTrancheAge.Age)) {
			max = DateHelper.differenceDate(new Date(), employeReposi.getMaxDateNaissance(false), 1);

		} else if (employeReposi.getMinDateEmbauche(false) != null && typeTranche.equals(ETypeTrancheAge.Experience)) {
			max = DateHelper.differenceDate(new Date(), employeReposi.getMaxDateEmbauche(false), 1);
		}

		/*
		 * min = typeTranche.equals(ETypeTrancheAge.Age)
		 * ? DateHelper.differenceDate(new Date(),
		 * employeReposi.getMinDateNaissance(false), 1)
		 * : DateHelper.differenceDate(new Date(),
		 * employeReposi.getMinDateEmbauche(false), 1);
		 * 
		 * max = typeTranche.equals(ETypeTrancheAge.Age)
		 * ? DateHelper.differenceDate(new Date(),
		 * employeReposi.getMaxDateNaissance(false), 1)
		 * : DateHelper.differenceDate(new Date(),
		 * employeReposi.getMaxDateEmbauche(false), 1);
		 */

		for (int i = min; i <= max; i = i + pas) {
			TrancheAge ta = new TrancheAge();
			ta.setBorneMin(i);
			ta.setBorneMax(i + pas);
			ta.setTypeTranche(typeTranche);

			String prefix = "";
			String suffix = "";
			if (getDefaultLang().contains("fr")) {
				prefix = "Entre ";
				suffix = " ans";
			} else if (getDefaultLang().contains("en")) {
				prefix = "Between ";
				suffix = " years";
			} else if (getDefaultLang().contains("sp")) {
				prefix = "entre ";
				suffix = " años";
			}

			ta.setIntitule(prefix + ta.getBorneMin() + " - " + ta.getBorneMax() + suffix);
			lta.add(ta);
		}

		return lta;

	}

	/*
	 * private String generateColor() {
	 * 
	 * Random random = new Random(); int newColor = random.nextInt(0x1000000);
	 * return String.format("#%06X", newColor); }
	 */

	@Override
	protected Logger getLogger() {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	protected Class<DashboardRh> getClazz() {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	protected List<ValidatorResult> validateEntity(DashboardRh record, EOperation operation) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	protected List<ValidatorResult> validateAllEntities(Set<DashboardRh> record, EOperation operation) {
		// TODO Auto-generated method stub
		return null;
	}

	private Date convertToDate(String receivedDate) throws ParseException {

		Date date = DateHelper.parse(receivedDate, "yyyy-MM-dd");
		return date;
	}

	private Date substractDate(int nbreAnnee) throws ParseException {

		Calendar date = Calendar.getInstance();

		nbreAnnee = nbreAnnee * (-1);

		date.roll(Calendar.YEAR, nbreAnnee);

		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");

		return convertToDate(formatter.format(date.getTime()).toString());
	}

	private Double ageMoyenEmp() {

		List<Employe> l = employeReposi.findByRadie(false);

		Double sum = 0.0;

		Double moyenne = 0.0;

		if (l != null && l.size() > 0) {
			for (Employe e : l) {

				if (e.getDateNaissance() != null)
					sum = sum + DateHelper.differenceDate(new Date(), e.getDateNaissance(), 1);
			}
			moyenne = sum / l.size();
		}

		return moyenne;
	}

	private Double experienceMoyenneEmp() {

		Parametre p = paramRepo.getPasTrancheAge();

		List<Employe> l = employeReposi.findByRadie(false);

		Double sum = 0.0;

		int nbreEmploye = 0;

		Double moyenne = 0.0;

		if (l != null && l.size() > 0) {

			if (p.getField3().equals(EAnneeExperience.DATE_EMBAUCHE.toString())) {
				for (Employe e : l) {
					if (e.getDateEmbauche() != null)
						sum = sum + DateHelper.differenceDate(new Date(), e.getDateEmbauche(), 1);
				}
			} else if (p.getField3().equals(EAnneeExperience.DATE_CONFIRMATION.toString())) {
				for (Employe e : l) {
					if (e.getDateConfirmation() != null)
						sum = sum + DateHelper.differenceDate(new Date(), e.getDateConfirmation(), 1);
					nbreEmploye += 1;
				}
			}
			moyenne = sum / nbreEmploye;
		}
		return moyenne;
	}

	public Double getMedianAgeOfEmploye() {
		// fetch all the employee
		List<Employe> employees = employeReposi.findByRadie(false);

		// Step 1: Calculate the ages of each employee
		List<Integer> ages = new ArrayList<>();
		Calendar today = Calendar.getInstance();
		double medianAge = 0;
		if (employees != null && employees.size() > 0) {
			employees.stream().forEach(employe -> {
				Calendar birthdate = Calendar.getInstance();
				Date dateNaissance = employe.getDateNaissance();
				int age = 0;
				if (dateNaissance != null) {
					birthdate.setTime(dateNaissance);
					age = today.get(Calendar.YEAR) - birthdate.get(Calendar.YEAR);
					if (today.get(Calendar.DAY_OF_YEAR) < birthdate.get(Calendar.DAY_OF_YEAR)) {
						age--;
					}
				}
				if (age != 0) {
					ages.add((int) age);
				}

			});
			// for (Employe employe : employees) {
			// Calendar birthdate = Calendar.getInstance();
			// Date dateNaissance = employe.getDateNaissance();
			// if (dateNaissance != null) {
			// birthdate.setTime(dateNaissance);
			// }
			//
			// int age = today.get(Calendar.YEAR) - birthdate.get(Calendar.YEAR);
			// if (today.get(Calendar.DAY_OF_YEAR) < birthdate.get(Calendar.DAY_OF_YEAR)) {
			// age--;
			// }
			//
			// ages.add((int) age);
			// }
			// Step 2: Sort the list of ages in ascending order
			// ages.sort(Integer::compareTo);

			// Step 3: Determine the median age

			int size = ages.size();
			// if (size % 2 == 0) {
			// medianAge = (ages.get(size / 2 - 1) + ages.get(size / 2)) / 2.0;
			// } else {
			// medianAge = ages.get(size / 2);
			// }
			int sum = 0;
			for (int age : ages) {
				sum = sum + age;
			}
			medianAge = sum / size;

			// System.out.println("Median Age: " + medianAge);
		}
		return medianAge;
	}

	public DashboardRh getDashboardInfoConge(EEtatDocument etat, String exercice, ETypeDocument type) {

		DashboardRh d = new DashboardRh();

		GraphData g = new GraphData();

		List<DemandeConge> ldc = demandeCongeRepo.findByExerciceAndEtatAndType(exercice, etat, type);
		List<NodeConge> nodeCongeList = nodeCongeRepo.findAll();

		if (ldc != null && !ldc.isEmpty() && nodeCongeList != null && !nodeCongeList.isEmpty()) {

			nodeCongeList.stream().forEach((nodeConge) -> {
				List<DemandeConge> listTypeConge = ldc.stream()
						.filter((conge) -> conge.getNodeConge().equals(nodeConge)).collect(Collectors.toList());

				g.getColors().add(nodeConge.getCouleur());
				g.getData().add((double) listTypeConge.size());
				g.getLabel().add(nodeConge.getIntitule());
			});
		}

		if (g != null)
			d.setGraphData(g);

		return d;
	}

	public DashboardRh getDashboardInfoCongeEnCours(EEtatDocument etat, String exercice, ETypeDocument type) {

		GraphData g = new GraphData();
		DashboardRh d = new DashboardRh(g);

		List<NodeConge> lnc = nodeCongeRepo.findAll();

		if (lnc != null)
			for (NodeConge nc : lnc) {
				g.getLabel().add(nc.getIntitule());
				g.getColors().add(nc.getCouleur());
				Long value = demandeCongeRepo
						.countByDateDebutBeforeAndDateFinAfterAndExerciceAndEtatAndTypeAndNodeConge(new Date(),
								new Date(), exercice, etat, type, nc);
				if (nc.getTypeConge().isAnnuel())
					d.setNbrDocGen(value);
				g.getData().add(Double.parseDouble(value + ""));
			}

		if (g != null)
			d.setGraphData(g);

		List<EEtatDocument> etats = new ArrayList<>();
		etats.add(EEtatDocument.REJETDERH);
		etats.add(EEtatDocument.REJETER);

		d.setNbrDocGenValid(demandeCongeRepo.countByExerciceAndEtatInAndType(exercice, etats, type));

		return d;
	}

	public DashboardRh getCongeByAgence(EEtatDocument etat, String exercice, ETypeDocument type) {

		DashboardRh dash = new DashboardRh();

		GraphData g = new GraphData();

		List<NodeConge> lnc = nodeCongeRepo.findAll();

		// int i = 0;

		Set<Noeud> noeud = null;
		try {
			noeud = noeudService.findAllAgences().getEntities();
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}

		if (noeud != null && lnc != null) {

			// for (NodeConge nc : lnc) {
			//
			// g.getColors().add(nc.getCouleur());
			// g.getSeries().add(nc.getIntitule());
			//
			// for (Noeud agence : noeud) {
			// if (i < noeud.size()) {
			// g.getLabel().add(agence.getCode());
			// i++;
			// }
			//
			// Long v = demandeCongeRepo.getNombreCongeAgence(exercice, etat, type, agence,
			// nc);
			// g.getData().add(Double.parseDouble(v + ""));
			// }
			// g.getDatas().add(new ArrayList<>(g.getData()));
			// g.getData().clear();
			// }
		}

		if (g != null)
			dash.setGraphData(g);
		return dash;
	}

	@Override
	public List<Fonction> getUnconfiguredFunction(String exercice) {
		Set<Fonction> functions = fonctionRepository.findByFonctionEvaluable(true);
		List<Fonction> unConfiguredFunctions = new ArrayList<Fonction>();
		if (functions != null && !functions.isEmpty()) {
			for (Fonction f : functions) {
				List<CritereParFonction> critereParFonctions = critereParFonctionRepo.findByFonctionAndExercice(f,
						exercice);
				if (critereParFonctions.isEmpty()) {
					unConfiguredFunctions.add(f); // If any function doesn't have criteria configured, add it tolist of
													// unconfigured fonction
				}
			}
		}
		return unConfiguredFunctions; // Return the list of unconfigured functions
	}

	// ==================== OPTIMIZED METHODS ====================

	/**
	 * Optimized agency graph data generation using single query
	 */
	private DashboardRh graphClasseOptimized(DashboardRh dash) {
		List<Object[]> agencyStats = employeReposi.getEmployeeCountByAgency(false);
		for (Object[] stat : agencyStats) {
			String agencyName = (String) stat[1];
			Long count = ((Number) stat[2]).longValue();

			dash.getLabelAgence().add(agencyName);
			dash.getDataAgence().add(count.doubleValue());
		}
		return dash;
	}

	/**
	 * Optimized age/experience range calculation using database aggregation
	 */
	private List<TrancheAge> getInfosByTypeTrancheAgeOptimized(ETypeTrancheAge typeAge) throws Exception {
		List<TrancheAge> listTranche = new ArrayList<>();
		Parametre p = paramRepo.getPasTrancheAge();

		if (p == null)
			return listTranche;

		int pas = (typeAge.equals(ETypeTrancheAge.Age)) ? Integer.parseInt(p.getField1())
				: Integer.parseInt(p.getField2());

		if (pas <= 0)
			return listTranche;

		// Get aggregated statistics from database
		List<Object[]> rangeStats = (typeAge.equals(ETypeTrancheAge.Age))
				? employeReposi.getAgeRangeStatistics(false, pas)
				: employeReposi.getExperienceRangeStatistics(false, pas);

		long totalEmployees = employeReposi.countByRadie(false);

		// Convert database results to TrancheAge objects
		for (Object[] stat : rangeStats) {
			Integer rangeStart = ((Number) stat[0]).intValue();
			Long count = ((Number) stat[1]).longValue();

			TrancheAge ta = new TrancheAge();
			ta.setBorneMin(rangeStart);
			ta.setBorneMax(rangeStart + pas);
			ta.setTypeTranche(typeAge);
			ta.setNbreEmploye(count);

			if (totalEmployees > 0) {
				ta.setPourcentageEmpl((count.doubleValue() / totalEmployees) * 100);
			}

			// Set localized label
			String prefix = getLocalizedPrefix();
			String suffix = getLocalizedSuffix();
			ta.setIntitule(prefix + ta.getBorneMin() + " - " + ta.getBorneMax() + suffix);

			listTranche.add(ta);
		}

		return listTranche;
	}

	private String getLocalizedPrefix() {
		if (getDefaultLang().contains("fr"))
			return "Entre ";
		else if (getDefaultLang().contains("en"))
			return "Between ";
		else if (getDefaultLang().contains("sp"))
			return "entre ";
		return "Between ";
	}

	private String getLocalizedSuffix() {
		if (getDefaultLang().contains("fr"))
			return " ans";
		else if (getDefaultLang().contains("en"))
			return " years";
		else if (getDefaultLang().contains("sp"))
			return " años";
		return " years";
	}

	/*
	 * private List<Fonction> getUnconfiguredFunction(String exercice){
	 * Set<Fonction> functions = fonctionRepository.findByFonctionEvaluable(true);
	 * List<Fonction> unConfiguredFunctions = new ArrayList<Fonction>();
	 * if (functions != null && !functions.isEmpty()) {
	 * for (Fonction f : functions) {
	 * List<CritereParFonction> critereParFonctions =
	 * critereParFonctionRepo.findByFonctionAndExercice(f, exercice);
	 * if (critereParFonctions.isEmpty()) {
	 * unConfiguredFunctions.add(f); // If any function doesn't have criteria
	 * configured, add it tolist of unconfigured fonction
	 * }
	 * }
	 * }
	 * return unConfiguredFunctions; //Return the list of unconfigured functions
	 * }
	 */
}
