server.port=8090
server.session.persistent=true
security.sessions= ALWAYS
#zuul.routes.ams_web.url=https://127.0.0.1:8061
#zuul.routes.ams_ws.url=https://127.0.0.1:8071
zuul.routes.ehr_web.url=http://127.0.0.1:8061
zuul.routes.ehr_ws.url=http://127.0.0.1:8071
#zuul.routes.ehr_web.url=https://127.0.0.1:8061
#zuul.routes.ehr_ws.url=https://127.0.0.1:8071
zuul.routes.files.path=file:///D:/photos/20180720/DCIM/sag/**
zuul.host.socket-timeout-millis=120000000
zuul.host.connect-timeout-millis=120000000
multipart.max-file-size=200000MB
multipart.max-request-size=200000MB
# Max file size.
spring.servlet.multipart.max-file-size=200000MB
# Max Request Size
spring.servlet.multipart.max-request-size=200000MB

server.tomcat.max-http-post-size=200000MB
server.tomcat.max-http-form-post-size=200000MB
server.tomcat.max-swallow-size=200000MB

# Route par défaut
zuul.routes.default.path=/**
#zuul.routes.default.url=https://127.0.0.1:8090/ehr_web
zuul.routes.default.url=http://127.0.0.1:8090/ehr_web

# zuul.routes.root.path=/
# zuul.routes.root.url=http://localhost:8061

# Autorise toutes les origines et toutes les m�thodes sur toutes les routes
spring.web.cors.allowed-origin-patterns=*
spring.web.cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
spring.web.cors.allowed-headers=*
spring.web.cors.allow-credentials=true