<!DOCTYPE html><html lang=fr><head><meta charset=UTF-8><meta name=viewport content="width=device-width, initial-scale=1.0"><title>Changement de mot de passe</title><link rel=stylesheet href=/css/font-awesome/css/font-awesome.min.css></head><body><div class=change-password-wrapper><div class=change-password-container><div class=form-section><div class=form-content><div class=logo-section><img src=./img/afb/logo_2.png alt=Logo class=auth-logo></div><div class=change-password-form-container><form name=form ng-submit=vm.changePwd() class=modern-form><div class="form-group modern-form-group mt-3"><label for=current class=modern-label><i class="fa fa-key"></i> {{"Current password"|translate}} <span class=form-required>*</span></label><div class="input-container password-input-container"><input type=password class=form-control name=current id=current required ng-model=vm.record.current placeholder="{{'Current password'|translate}}"> <span class=toggle-password ng-click=vm.togglePasswordOld() style="position: absolute; top: 50%;right: 10px;transform: translateY(-50%);cursor: pointer;font-size: 18px;">👁️</span></div><div ng-messages=form.current.$error ng-if=form.current.$dirty class=error-messages><div ng-message=required class=error-message>{{'REQFIELD' | translate}}</div></div></div><div class="form-group modern-form-group mt-3"><label for=password class=modern-label><i class="fa fa-lock"></i> {{"New password"|translate}} <span class=form-required>*</span></label><div class="input-container password-input-container"><input type=password class=form-control id=password name=password required ng-model=vm.record.password placeholder="{{'New password'|translate}}"> <span class=toggle-password ng-click=vm.togglePassword() style="position: absolute; top: 50%;right: 10px;transform: translateY(-50%);cursor: pointer;font-size: 18px;">👁️</span></div><div ng-messages=form.password.$error ng-if=form.password.$dirty class=error-messages><div ng-message=required class=error-message>{{'REQFIELD' | translate}}</div></div></div><div class="form-group modern-form-group mt-3"><label for=confirmation class=modern-label><i class="fa fa-check-circle"></i> {{"Password confirmation"|translate}} <span class=form-required>*</span></label><div class="input-container password-input-container"><input type=password class=form-control id=confirmation name=confirmation required ng-model=vm.record.confirmation placeholder="{{'Password confirmation'|translate}}"> <span class=toggle-password ng-click=vm.togglePasswordConfirmation() style="position: absolute; top: 50%;right: 10px;transform: translateY(-50%);cursor: pointer;font-size: 18px;">👁️</span></div><div ng-messages=form.confirmation.$error ng-if=form.confirmation.$dirty class=error-messages><div ng-message=required class=error-message>{{'REQFIELD' | translate}}</div></div></div><div class=password-strength ng-show="vm.record.password && vm.record.password.length > 0"><div class=strength-details><div class=strength-label>{{'Force_du_mot_de_passe_:'|translate}}</div><div class=strength-score>{{vm.record.password.length}} {{'Caracteres'|translate}}</div></div><div class=strength-bar><div class=strength-fill ng-class=vm.getPasswordStrengthClass()></div></div><div class=strength-text ng-class=vm.getPasswordStrengthClass() ng-bind=vm.getPasswordStrengthText()></div></div><div class=password-match-indicator ng-class=vm.getPasswordMatchClass() ng-show=vm.record.confirmation><i class=fa ng-class="vm.passwordsMatch() ? 'fa-check' : 'fa-times'"></i> <span ng-bind=vm.getPasswordMatchText()></span></div><div class=cancel-section><a class=cancel-link ng-click=vm.close()><i class="fa fa-times"></i> {{"Cancel" | translate}}</a></div><button type=submit class=modern-submit-btn ng-disabled=form.$invalid><span class=btn-content><i class="fa fa-save"></i> <span>{{ "Change password" | translate }}</span></span><div class=btn-loading ng-if=vm.isLoading><i class="fa fa-spinner fa-spin"></i></div></button></form><div class=security-tips><div class=tips-header><i class="fa fa-lightbulb-o"></i> <span>{{ "Conseils_pour_un_mot_de_passe_securisee" | translate }}</span></div><ul class=tips-list><li>{{ "Au_moins_8_caracteres" | translate }}</li><li>{{ "Melange_de_majuscules_et_minuscules" | translate }}</li><li>{{ "Inclure_des_chiffres_et_symboles" | translate }}</li><li>{{ "Eviter_les_informations_personnelles" | translate }}</li></ul></div></div></div></div><div class=background-section><div class=background-overlay></div></div></div></div><style>



/* Variables CSS */
:root {
    --primary-red: #DD3333;
    --primary-red-hover: #bb2222;
    --primary-red-light: rgba(221, 51, 51, 0.1);
    --gray-50: #f8f9fa;
    --gray-100: #e9ecef;
    --gray-200: #dee2e6;
    --gray-300: #ced4da;
    --gray-400: #adb5bd;
    --gray-500: #6c757d;
    --gray-600: #495057;
    --gray-700: #343a40;
    --gray-800: #212529;
    --gray-900: #1a1a1a;
    --white: #ffffff;
    --success: #28a745;
    --warning: #ffc107;
    --danger: #dc3545;
    --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.175);
    --border-radius: 8px;
    --border-radius-lg: 12px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Reset et base */
* {
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: var(--gray-800);
    background: var(--gray-50);
    margin: 0;
    padding: 0;
}

/* Container principal */
.change-password-wrapper {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--gray-100) 100%);
}

.change-password-container {
    width: 100%;
    max-width: 1200px;
    background: var(--white);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    display: grid;
    grid-template-columns: 1fr 1.2fr;
    min-height: 600px;
}

/* Section formulaire */
.form-section {
    padding: 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    background: var(--white);
}

.form-content {
    width: 100%;
    max-width: 400px;
    margin: 0 auto;
}

/* Logo */
.logo-section {
    text-align: center;
    margin-bottom: 32px;
}

.auth-logo {
    max-width: 180px;
    height: auto;
    object-fit: contain;
}

/* Container du formulaire */
.change-password-form-container {
    width: 100%;
}

/* En-tête du formulaire */
.form-header {
    text-align: center;
    margin-bottom: 32px;
}

.header-icon {
    width: 64px;
    height: 64px;
    background: linear-gradient(135deg, var(--primary-red), var(--primary-red-hover));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    box-shadow: var(--shadow);
}

.header-icon i {
    font-size: 24px;
    color: var(--white);
}

.form-title {
    font-size: 28px;
    font-weight: 700;
    color: var(--primary-red);
    margin: 0 0 8px 0;
    line-height: 1.3;
}

.form-subtitle {
    color: var(--gray-600);
    font-size: 16px;
    margin: 0;
    line-height: 1.5;
}

/* Formulaire moderne */
.modern-form {
    width: 100%;
}

.modern-form-group {
    margin-bottom: 24px;
}

.modern-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: 8px;
    font-size: 14px;
}

.modern-label i {
    color: var(--primary-red);
    width: 16px;
    text-align: center;
}

.form-required {
    color: var(--primary-red);
    font-weight: 700;
}

/* Conteneurs d'input */
.input-container {
    position: relative;
}



/* Bordure de focus animée */

/* Container mot de passe */
.password-input-container {
    position: relative;
}

.password-toggle {
    position: absolute !important;
    right: 16px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    background: none !important;
    border: none !important;
    color: var(--gray-500) !important;
    cursor: pointer !important;
    padding: 8px !important;
    border-radius: 4px !important;
    transition: var(--transition) !important;
    z-index: 10 !important;
    width: 32px !important;
    height: 32px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.password-toggle:hover {
    color: var(--primary-red) !important;
    background: var(--primary-red-light) !important;
}

/* Messages d'erreur */
.error-messages {
    margin-top: 4px;
}

.error-message {
    color: var(--danger);
    font-size: 12px;
    margin: 0;
}

/* Indicateur de force du mot de passe */
.password-strength {
    margin: 16px 0 24px 0;
    padding: 16px;
    background: var(--gray-50);
    border-radius: var(--border-radius);
    border: 1px solid var(--gray-200);
}

.strength-label {
    font-size: 12px;
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: 8px;
}

.strength-bar {
    width: 100%;
    height: 4px;
    background: var(--gray-200);
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 8px;
}

.strength-fill {
    height: 100%;
    transition: all 0.3s ease;
    border-radius: 2px;
}

.strength-fill.weak {
    width: 25%;
    background: var(--danger);
}

.strength-fill.fair {
    width: 50%;
    background: var(--warning);
}

.strength-fill.good {
    width: 75%;
    background: #17a2b8;
}

.strength-fill.strong {
    width: 100%;
    background: var(--success);
}

.strength-text {
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
}

/* Section d'annulation */
.cancel-section {
    text-align: right;
    margin-bottom: 24px;
}

.cancel-link {
    color: var(--primary-red) !important;
    text-decoration: none !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    display: inline-flex !important;
    align-items: center !important;
    gap: 4px !important;
    transition: var(--transition) !important;
    cursor: pointer !important;
}

.cancel-link:hover {
    color: var(--primary-red-hover) !important;
    text-decoration: underline !important;
}

/* Bouton de soumission */
.modern-submit-btn {
    width: 100% !important;
    padding: 16px 24px !important;
    background: linear-gradient(135deg, var(--primary-red) 0%, var(--primary-red-hover) 100%) !important;
    color: var(--white) !important;
    border: none !important;
    border-radius: var(--border-radius) !important;
    font-size: 16px !important;
    font-weight: 600 !important;
    cursor: pointer !important;
    transition: var(--transition) !important;
    position: relative !important;
    overflow: hidden !important;
    box-shadow: var(--shadow) !important;
    margin-bottom: 24px !important;
}

.modern-submit-btn:hover:not(:disabled) {
    transform: translateY(-2px) !important;
    box-shadow: var(--shadow-lg) !important;
    background: linear-gradient(135deg, var(--primary-red-hover) 0%, #aa1111 100%) !important;
}

.modern-submit-btn:active {
    transform: translateY(0) !important;
}

.modern-submit-btn:disabled {
    background: var(--gray-300) !important;
    cursor: not-allowed !important;
    transform: none !important;
    box-shadow: var(--shadow-sm) !important;
}

.btn-content {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 8px !important;
}

.btn-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

/* Conseils de sécurité */
.security-tips {
    background: rgba(23, 162, 184, 0.05);
    border: 1px solid rgba(23, 162, 184, 0.2);
    border-radius: var(--border-radius);
    padding: 20px;
}

.tips-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 12px;
    font-size: 14px;
}

.tips-header i {
    color: #17a2b8;
}

.tips-list {
    margin: 0;
    padding-left: 20px;
    color: var(--gray-600);
}

.tips-list li {
    font-size: 13px;
    margin-bottom: 4px;
}

/* Section arrière-plan avec image */
.background-section {
    background-image: url('./img/bg-login.jpeg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.background-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(221, 51, 51, 0.6) 0%, rgba(187, 34, 34, 0.8) 100%);
}

/* Responsive Design */
@media (max-width: 968px) {
    .change-password-container {
        grid-template-columns: 1fr;
        max-width: 500px;
    }
    
    .background-section {
        display: none;
    }
    
    .form-section {
        padding: 32px 24px;
    }
}

@media (max-width: 576px) {
    .change-password-wrapper {
        padding: 16px;
    }
    
    .form-section {
        padding: 24px 20px;
    }
    
    .form-title {
        font-size: 24px;
    }
    
    .form-control {
        padding: 14px 16px !important;
        font-size: 16px !important;
    }
    
    .modern-submit-btn {
        padding: 14px 20px !important;
    }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.form-content {
    animation: fadeInUp 0.6s ease-out;
}

.modern-form-group {
    animation: fadeInUp 0.6s ease-out;
    animation-fill-mode: both;
}

.modern-form-group:nth-child(1) { animation-delay: 0.1s; }
.modern-form-group:nth-child(2) { animation-delay: 0.2s; }
.modern-form-group:nth-child(3) { animation-delay: 0.3s; }

.modern-submit-btn {
    animation: fadeInUp 0.6s ease-out 0.4s both;
}

/* États de validation */
.form-control.ng-invalid.ng-dirty {
    border-color: var(--danger) !important;
}

.form-control.ng-valid.ng-dirty {
    border-color: var(--success) !important;
}

/* Focus visible pour l'accessibilité */
.modern-submit-btn:focus-visible,
.password-toggle:focus-visible,
.cancel-link:focus-visible {
    outline: 2px solid var(--primary-red) !important;
    outline-offset: 2px !important;
}

.password-match-indicator {
    margin-top: 8px;
    padding: 8px 12px;
    border-radius: var(--border-radius);
    font-size: 12px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 6px;
}

.password-match-indicator.match {
    background: rgba(40, 167, 69, 0.1);
    color: var(--success);
    border: 1px solid rgba(40, 167, 69, 0.2);
}

.password-match-indicator.no-match {
    background: rgba(220, 53, 69, 0.1);
    color: var(--danger);
    border: 1px solid rgba(220, 53, 69, 0.2);
}

/* Suggestions d'amélioration */
.password-suggestions {
    margin-top: 12px;
    padding: 12px;
    background: rgba(255, 193, 7, 0.1);
    border: 1px solid rgba(255, 193, 7, 0.2);
    border-radius: var(--border-radius);
}

.suggestions-title {
    font-size: 12px;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 6px;
}

.suggestions-title i {
    color: var(--warning);
}

.suggestions-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.suggestions-list li {
    font-size: 11px;
    color: var(--gray-600);
    margin-bottom: 4px;
    padding-left: 16px;
    position: relative;
}

.suggestions-list li:before {
    content: "•";
    color: var(--warning);
    position: absolute;
    left: 0;
}

/* Amélioration de l'indicateur de force */
.password-strength {
    margin: 16px 0 24px 0;
    padding: 16px;
    background: var(--gray-50);
    border-radius: var(--border-radius);
    border: 1px solid var(--gray-200);
    transition: all 0.3s ease;
}

.password-strength.visible {
    opacity: 1;
    transform: translateY(0);
}

.password-strength.hidden {
    opacity: 0;
    transform: translateY(-10px);
}

.strength-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.strength-label {
    font-size: 12px;
    font-weight: 600;
    color: var(--gray-700);
}

.strength-score {
    font-size: 11px;
    color: var(--gray-500);
    background: var(--white);
    padding: 2px 8px;
    border-radius: 12px;
    border: 1px solid var(--gray-200);
}

/* Couleurs pour les différents niveaux de force */
.strength-text.weak {
    color: var(--danger);
}

.strength-text.fair {
    color: var(--warning);
}

.strength-text.good {
    color: #17a2b8;
}

.strength-text.strong {
    color: var(--success);
}

/* Animation pour la barre de progression */
.strength-fill {
    height: 100%;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 2px;
    position: relative;
    overflow: hidden;
}

.strength-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Responsive pour les nouvelles fonctionnalités */
@media (max-width: 576px) {
    .password-strength {
        padding: 12px;
    }
    
    .strength-details {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }
    
    .password-suggestions {
        padding: 10px;
    }
    
    .suggestions-list li {
        font-size: 10px;
    }
}
</style></body></html>