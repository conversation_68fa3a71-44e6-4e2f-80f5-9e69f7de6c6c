package org.pkfrc.core.entities;

import java.io.Serializable;

import javax.persistence.MappedSuperclass;
import javax.persistence.Transient;
import javax.persistence.Version;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;
import lombok.EqualsAndHashCode;

@MappedSuperclass
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public abstract class AbstractEntity<T> implements Serializable {

	private static final long serialVersionUID = 1L;

	@EqualsAndHashCode.Include
	protected T id;

	protected Long version;

	protected String exerc;

	public abstract T getId();

	@Transient
	public String toStringId() {
		return String.valueOf(id);
	}

	public void setId(T id) {
		this.id = id;
	}

	@Version
	public Long getVersion() {
		return version;
	}

	public void setVersion(Long version) {
		this.version = version;
	}

	public AbstractEntity<T> build(T id, Long version) {
		this.id = id;
		this.version = version;
		return this;
	}

	public AbstractEntity<T> build(T id) {
		this.id = id;
		return this;
	}

	public String getExerc() {
		return exerc;
	}

	public void setExerc(String exerc) {
		this.exerc = exerc;
	}

}
