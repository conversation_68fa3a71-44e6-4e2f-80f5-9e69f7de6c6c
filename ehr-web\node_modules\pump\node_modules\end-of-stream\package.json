{"_from": "end-of-stream@^1.1.0", "_id": "end-of-stream@1.4.5", "_inBundle": false, "_integrity": "sha512-ooEGc6HP26xXq/N+GCGOT0JKCLDGrq2bQUZrQ7gyrJiZANJ/8YDTxTpQBXGMn+WbIQXNVpyWymm7KYVICQnyOg==", "_location": "/pump/end-of-stream", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "end-of-stream@^1.1.0", "name": "end-of-stream", "escapedName": "end-of-stream", "rawSpec": "^1.1.0", "saveSpec": null, "fetchSpec": "^1.1.0"}, "_requiredBy": ["/pump"], "_resolved": "https://registry.npmjs.org/end-of-stream/-/end-of-stream-1.4.5.tgz", "_shasum": "7344d711dea40e0b74abc2ed49778743ccedb08c", "_spec": "end-of-stream@^1.1.0", "_where": "D:\\Dev projets\\PKF_Work\\EHR\\ehr_afb\\ehr-web\\node_modules\\pump", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/mafintosh/end-of-stream/issues"}, "bundleDependencies": false, "dependencies": {"once": "^1.4.0"}, "deprecated": false, "description": "Call a callback when a readable/writable/duplex stream has completed or failed.", "devDependencies": {"tape": "^4.11.0"}, "files": ["index.js"], "homepage": "https://github.com/mafintosh/end-of-stream", "keywords": ["stream", "streams", "callback", "finish", "close", "end", "wait"], "license": "MIT", "main": "index.js", "name": "end-of-stream", "repository": {"type": "git", "url": "git://github.com/mafintosh/end-of-stream.git"}, "scripts": {"test": "node test.js"}, "version": "1.4.5"}