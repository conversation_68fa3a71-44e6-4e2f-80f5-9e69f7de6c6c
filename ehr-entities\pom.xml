 <project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.pkfrc.ehr</groupId>
		<artifactId>ehr-parent</artifactId>
		<version>0.0.1-SNAPSHOT</version>
		<relativePath>../ehr-parent/pom.xml</relativePath>
	</parent>
	<artifactId>ehr-entities</artifactId>
	<name>E-Human-Ressources JPA</name>
	<dependencies>
		<dependency>
			<groupId>pkfrc-core</groupId>
			<artifactId>core-entities</artifactId>
			<version>0.0.1-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>org.scala-lang</groupId>
			<artifactId>scala-library</artifactId>
			<version>2.11.0</version>
		</dependency>
		<dependency>
		<groupId>org.projectlombok</groupId>
		<artifactId>lombok</artifactId>
		<version>1.18.30</version>
		<scope>provided</scope>
		</dependency>
		<!--<dependency>
			<groupId>pkfrc-core</groupId>
			<artifactId>core-services</artifactId>
			<version>0.0.1-SNAPSHOT</version>
		</dependency>-->
		<!--<dependency>
			<groupId>pkfrc-core</groupId>
			<artifactId>core-authentication</artifactId>
			<version>0.0.1-SNAPSHOT</version>
		</dependency>-->
	</dependencies>
</project>