package org.pkfrc.ehr.repo.sanction;

import java.util.Date;
import java.util.List;
import java.util.Set;

import org.pkfrc.ehr.entities.sanction.Sanction;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface SanctionRepository extends JpaRepository<Sanction, Long> {

	@Query("SELECT dc FROM Sanction dc"
			+ " INNER JOIN dc.employe em"
			+ " WHERE "
			+ " dc.statut = org.pkfrc.ehr.entities.sanction.enums.ESanctionStatus.VALIDER_SANCTION AND "
			+ " ( dc.dateDebut BETWEEN :debut AND :fin  OR "
			+ "   dc.dateFin BETWEEN :debut AND :fin) AND "
			+ " em.matricule = :matricule ")
	Set<Sanction> getSanctionBetween(@Param("debut") Date debut, @Param("fin") Date fin,
			@Param("matricule") String matricule);

	@Query("SELECT max(id) FROM Sanction")
	Long getMaxID();

	Set<Sanction> findByEmployeMatricule(String matricule);

	/* Nombre total des sanctions d'un employe par type sanction */
	@Query(value = "SELECT tpsanc.intitule, COUNT(sanc) FROM pkf_ehr_sanction as sanc " +
			"INNER JOIN pkf_ehr_employe as emp ON emp.id_personne = sanc.employe_id " +
			"INNER JOIN pkf_ehr_type_sanction as tpsanc ON tpsanc.id = sanc.type_sanction_id " +
			"WHERE emp.emp_matricule = :matricule " +
			"GROUP BY tpsanc.intitule", nativeQuery = true)
	List<Object[]> getEmployeeTotalSanctionByTypeSanction(
			@Param("matricule") String matricule);
	/* Fin Nombre total des sanctions d'un employe par type sanction */

	Sanction findById(Long id);

	List<Sanction> findByExercice(String exercice);

	@Query("SELECT dc FROM Sanction dc"
			+ " WHERE "
			+ " dc.statut = org.pkfrc.ehr.entities.sanction.enums.ESanctionStatus.VALIDER_SANCTION AND "
			+ " ( dc.dateDebut BETWEEN :debut AND :fin  OR "
			+ "   dc.dateFin BETWEEN :debut AND :fin)  ")
	Set<Sanction> getListSanctionBetween(@Param("debut") Date debut, @Param("fin") Date fin);

	// Optimized count query instead of loading all entities
	@Query("SELECT COUNT(s) FROM Sanction s INNER JOIN s.employe e WHERE e.matricule = :matricule")
	long countByEmployeMatricule(@Param("matricule") String matricule);

}
