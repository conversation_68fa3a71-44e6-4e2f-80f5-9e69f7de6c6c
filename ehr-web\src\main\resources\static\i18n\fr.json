{"Classe.Exist.In.List ": "", "Liste_retours_effectifs_cong�s": "Liste des retours effectifs", "Upload.Contains.Empty.Rows": "Ligne(s) vide(s) présente(s)", "Périodique ": "Périodique", "EtapeRecrutement": "Étape de recrutement", "Besoin en recrutement": "Besoin en recrutement", "Campagnes recrutement": "Campagnes de recrutement", "PVs de recrutement": "PV de recrutement", "Candidats": "Candidats", "Postes": "<PERSON><PERSON>", "Candidat": "Candidat", "dateDebut": "Date de début", "Groupes rubriques": "Groupes de rubriques", "Tableau de bord": "Tableau de bord", "Repports": "Rapport", "Dipes Magnetiques": "Dipes Magnétiques", "Etats_locaux": "États locaux", "Etats_specifiques": "États spécifiques", "Analyse encours": "Analyse en cours", "Filtres sur les employé": "Filtres sur les employés", "Supprimer les calculs": "Supprimer les calculs", "Genrer les Bulletins": "Générer les bulletins", "Liste des calculs paies employees": "Liste des calculs de paie des employés", "Chapitre": "Chapitre", "Comptes des Agences": "Comptes des agences", "Comptes de liaison": "Comptes de liaison", "Ecritures comptables": "Écritures comptables", "Liste des chapitres": "Liste des chapitres", "Comptabilite agence": "Comptabilité agence", "Agence de liaison": "Agence de liaison", "Generation des écritures comptbales": "Génération des écritures comptables", "Configurations Mission": "Configurations mission", "Rubriques Missions": "Rubriques missions", "Planning_missions_validés": "Planning missions validés", "Type de Sanction": "Type de Sanction", "Type de note disciplinaire": "Type de note disciplinaire", "Gestion des sanctions": "Gestion des sanctions", "Type_Pret": "Type de prêt", "Type de pret": "Type de prêt", "Liste des remboursements": "Liste des remboursements", "Date de remboursement": "Date de remboursement", "Type de remboursement": "Type de remboursement", "Actions": "Actions", "Détails du pret": "<PERSON><PERSON><PERSON>ê<PERSON>", "Détails employé": "<PERSON><PERSON><PERSON> employé", "Gestion des Prets": "Gestion des prêts", "Médécine de travail": "Médecine de travail", "Médécins": "M<PERSON><PERSON><PERSON><PERSON>", "Laboratoires": "Laboratoires", "Liste des laboratoires": "Liste des laboratoires", "Laboratoires d analyse": "Laboratoires d'analyses", "Examens médicaux": "Examens médicaux", "Liste des types d examen": "Liste des types d'examens", "Liste des types de visite médicales": "Liste des types de visites médicales", "Liste des spécialisations": "Liste des spécialisations", "Liste des convocations médicales employés": "Liste des convocations médicales des employés", "Spécialisations": "Spécialisations", "Périodique ?": "Périodique ?", "Hopital": "Hôpital", "Noms*": "Noms*", "Prénoms*": "Prénoms*", "Evenementiel ?": "Événementiel ?", "Evènement": "Événement", "Nombre de jours avant date limite*": "Nombre de jours avant date limite*", "Génération automatique des convocations*": "Génération automatique des convocations*", "Examens à faire": "Examens à faire", "Détails convocation médicale": "Détails de la convocation médicale", "Résultats examens": "Résultats des examens", "Détails examens à faire": "Détails des examens à faire", "Hopitaux agréés": "Hôpitaux agréés", "Laboratoires agréés": "Laboratoires agréés", "Téléphone": "Téléphone", "Spécialisation": "Spécialisation", "matricule": "Matricule", "Intitule visite médicale": "Intitulé visite médicale", "Date convocation": "Date de convocation", "Date limite": "Date limite", "Nombre de jours": "Nombre de jours", "Visite médicale": "Visite médicale", "Examen(s) restant(s) à faire": "Examen(s) restant(s) à faire", "Date d' envoie alerte": "Date d’envoi alerte", "Planning Missions Valides": "Planning missions validés", "Configuration Mission": "Configuration mission", "Liste des formations": "Liste des formations", "VERIFICATION DES CONFIGURATIONS COMPTABILITE": "VÉRIFICATION DES CONFIGURATIONS COMPTABILITÉ", "Configuration des comptes de rubriques ": "Configuration des comptes de rubriques", "Configuration des comptes des agences de liaison": "Configuration des comptes des agences de liaison", "Generer les écritures comptables": "Générer les écritures comptables", "Sélectionner": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PERIODE PAIE": "PÉRIODE PAIE", "Congé parental": "Con<PERSON> parental", "Absences standards": "Absences standards", "Arret maladie": "<PERSON><PERSON><PERSON><PERSON> maladie", "congé de repos": "Congé de repos", "Deuil": "<PERSON><PERSON>", "training": "Training", "Planning Conges Valides": "Planning congés validés", "Planning Conges Annuels": "Planning congés annuels", "Generer les bulletins": "Générer les bulletins", "Supprimer les cacules": "Supprimer les calculs", "RubriqueErreur": "<PERSON><PERSON><PERSON><PERSON> erreur", "Calcule": "Calcul", "Type Sanction": "Type Sanction", "Sanction": "Sanction", "Type Note Disciplinaire": "Type Note Disciplinaire", "Rubriques Mission": "Rubriques mission", "Descritpion": "Description", "Recrutement": "Recrutement", "jour(s)": "jour(s)", "jour": "jour", "heures": "heures", "En cours": "En cours", "Expiré": "Expiré", "Niveau Formation": "Niveau formation", "Liste des niveaux de formation": "Liste des niveaux de formation", "Categorie formation": "Catégorie formation", "Sessions formations": "Sessions de formation", "Liste des sessions de formations": "Liste des sessions de formation", "Liste_categorie_Formations": "Liste des catégories de formations", "Liste des types de formations": "Liste des types de formations", "TypeFormation": "Type de formation", "Date debut formation": "Date de début formation", "Demande de pret": "<PERSON><PERSON><PERSON>", "Nombre échéance": "Nombre d'échéances", "Motif de refus": "<PERSON><PERSON><PERSON>", "Type Pret": "Type de prêt", "Brochure": "Brochure", "Justificatif Retour conge supplementaire": "Justificatif retour congé supplémentaire", "Demande de formation": "Demande de formation", "Justificatif Mission": "Justificatif de mission", "Justificatif Note Disciplaire": "Justificatif note disciplinaire", "Resultats examens": "Résultats des examens", "Notes de Service": "Notes de Service", "Langue": "<PERSON><PERSON>", "Contacts d urgence": "Contacts d'urgence", "Contacts Urgence": "Contacts d'urgence", "Employés radiés": "Employés radiés", "Parler": "<PERSON><PERSON><PERSON>", "Ecrire": "<PERSON><PERSON><PERSON><PERSON>", "Liste_demandes_Missions": "Liste des demandes de missions", "Reject": "<PERSON><PERSON><PERSON>", "Motif_Rejet": "<PERSON><PERSON><PERSON>", "Type Absence": "Type Absence", "Nombre de Jours": "Nombre de jours", "Fin Previsionnelle": "Fin prévisionnelle", "Date Demande": "Date de demande", "Details Congés": "<PERSON><PERSON><PERSON> congés", "Nombre de jours validés": "Nombre de jours validés", "Rejeté par le manager": "Rejeté par le manager", "Rejeté par la RH": "Rejeté par la RH", "En attente de validation": "En attente de validation", "Validé par la RH": "Validé par la RH", "Validé par le manager": "Validé par le manager", "Demande enregistré": "<PERSON><PERSON><PERSON> enre<PERSON><PERSON>", "Votre demande a été rejeté par la RH.": "Votre demande a été rejetée par la RH.", "Votre demande est en attente de validation par votre manager.": "Votre demande est en attente de validation par votre manager.", "Votre demande a été validé par votre manager et est en attente de validation par la RH.": "Votre demande a été validée par votre manager et est en attente de validation par la RH.", "Votre demande a été validé par la RH.": "Votre demande a été validée par la RH.", "Votre demande a été envoyé.": "Votre demande a été envoyée.", "Rejeté par votre manager.": "Rejeté par votre manager.", "Liste des prets": "Liste des prêts", "Retour Absence": "Retour absence", "Paramètres Demande de pret": "Paramètres demande de prê<PERSON>", "Configuration du pourcentage de limitation des demandes de pret": "Configuration du pourcentage de limitation des demandes de prêt", "Pourcentage": "Pourcentage", "Entrez une valeur entre 1 et 100": "Entrez une valeur entre 1 et 100", "Aide": "Aide", "Guide d'utilisateur": "Guide d'utilisateur", "Guide utilisateur Employé": "Guide utilisateur Employé", "Guide utilisateur Manager": "Guide utilisateur Manager", "Guide utilisateur RH": "Guide utilisateur RH", "Guide utilisateur Administrateur": "Guide utilisateur Administrateur", "Montant élevé, vous avez atteint votre quota mensuel !": "<PERSON><PERSON>, vous avez atteint votre quota mensuel !", "Reset password": "Changer le mot de passe", "Suspendre": "<PERSON><PERSON><PERSON><PERSON>", "Activer": "Activer", "Choose an option": "Choisir une option", "Exporter les bulletins de paie": "Exporter les bulletins de paie", "Maladie": "Maladie", "Mariage": "Mariage", "Entre 0 - 5 ans": "Entre 0 - 5 ans", "Entre 5 - 10 ans": "Entre 5 - 10 ans", "Entre 10 - 15 ans": "Entre 10 - 15 ans", "Entre 15 - 20 ans": "Entre 15 - 20 ans", "Entre 20 - 25 ans": "Entre 20 - 25 ans", "Entre 0 - 10 ans": "Entre 0 - 10 ans", "adresse": "<PERSON><PERSON><PERSON>", "téléphone": "Téléphone", "Parametrage Tableau de Bord": "Paramétrage du tableau de bord", "Age": "Âge", "Années d'expérience": "Années d'expérience", "Couleur diagramme": "Couleur diagramme", "Nombres de jours ouvrables sur l'exercice": "Nombres de jours ouvrables sur l'exercice", "Calcul année d'expérience": "Calcul année d'expérience", "DATE_EMBAUCHE": "DATE_EMBAUCHE", "DATE_CONFIRMATION": "DATE_CONFIRMATION", "Previous": "Précédent", "Choisir un fichier": "<PERSON><PERSON> un fichier", "Aucun fichier choisir": "<PERSON><PERSON><PERSON> fi<PERSON><PERSON> choisi", "Upload": "Upload", "Close": "<PERSON><PERSON><PERSON>", "Action": "Action", "Structure": "Structure", "Type de noeuds": "Type de nœuds", "Test de connexion": "Test de connexion", "Liste de object json": "Liste d'objets JSON", "Sources REST": "Sources REST", "Sanctions manager": "Sanctions manager", "Retour Effectif": "Retour effectif", "Realisation": "Réalisation", "Mes operations": "Mes opérations", "Repporting realisations": "Reporting réalisations", "Realisation mensuelle": "Réalisation mensuelle", "Criteres Objectifs": "Critères objectifs", "Manager": "Manager", "Saction RH": "Sanction RH", "Type d'affectation": "Type d'affectation", "Liste des contrats": "Liste des contrats", "Affectations": "Affectations", "Element": "É<PERSON>ment", "Tyde contrat": "Type de contrat", "Etapes de recrutement": "Étapes de recrutement", "Besoins en recrutement": "Besoins en recrutement", "Date fin": "Date de fin", "Date Ouverture": "Date d'ouverture", "Date fermeture": "Date de fermeture", "Code campagne": "Code campagne", "Choisir une option de generation": "Choisir une option de génération", "Generer les normes selon les configurations criteres par fonction": "Générer les normes selon les configurations critères par fonction", "Generer les normes sur la base des normes ou objectifs precedentes": "Générer les normes sur la base des normes ou objectifs précédentes", "Retour": "Retour", "Suivant": "Suivant", "Reinitialiser documents": "Réinitialiser documents", "Label": "Label", "level": "Niveau", "Field 1": "Field 1", "Remboursé": "Re<PERSON><PERSON><PERSON>", "Aucune sélection": "Aucune sélection", "En attente": "En attente", "Suspendu": "Suspendu", "Rejetté": "<PERSON><PERSON><PERSON>", "Pret redondant!!!..Un pret de ce type est déjà en attente ou en cours !": "Prêt redondant !!! Un prêt de ce type est déjà en attente ou en cours !", "Montant élévé, vous avez atteint votre quota mensuel !": "<PERSON><PERSON>, vous avez atteint votre quota mensuel !", "Vous n'avez pas encore reçu de paie via EHR !": "Vous n'avez pas encore reçu de paie via EHR !", "Tous les documents": "Tous les documents", "Frais Mission": "Frais mission", "Planning Missions Validés": "Planning missions validés", "Pièces Justificatives": "Pièces justificatives", "AllManagerDocuments": "AllManagerDocuments", "Tableau_Postes": "Tableau de poste", "Generer_contribution_documentation": "Générer l'état de contribution", "Agence_virement": "Agence de virement", "Type_Etat_contribution": "Choisir l'état de contribution", "Liste_documents": "Liste des documents", "AllDocuments": "Documents", "Fiche_Poste": "Fiche de poste", "Liste_Sanctions": "Liste des sanctions", "Poste_Exercice": "Poste d'exercice", "Liste_Poste": "Liste des postes d'exercice", "Lieu_Naissance": "Lieu de naissance", "LISTE_FICHE_POSTE": "Liste des fiches de poste", "Acte_num": "Numéro acte", "Liste_Natures ": "Liste de natures", "En_Fin_Relation": "En fin de relation", "Détails_Modifiation_Profile": "Détails modification profil", "Détails_Original_employé": "<PERSON><PERSON><PERSON> original employé", "Modification_demandé_par_employé": "Modification demandée par employé", "Type_Documents": "Type de documents", "Etats_documents": "États des documents", "Etat_contributions": "Générer les documents de contributions", "TYpe_contribution": "Type de contributions", "Fichier Excel": "Fichier Excel", "Fichier Unl": "<PERSON><PERSON><PERSON>", "Configuration des comptes pour chaque employé": "Configuration des comptes pour chaque employé", "Configuration des comptes de rubriques pour chaque agence": "Configuration des comptes de rubriques pour chaque agence", "Configuration des comptes de liaison pour chaque agence": "Configuration des comptes de liaison pour chaque agence", "Liste des comptes employés non configurés": "Liste des comptes employés non configurés", "Liste des agences avec des comptes de liaison non configurés": "Liste des agences avec des comptes de liaison non configurés", "ATTENTE_SANCTION": "Sanction en attente", "VALIDER_SANCTION": "Sanction validé", "REJECT": "<PERSON><PERSON><PERSON>", "Rubrique_journalisation": "Rubriques de journalisation", "Type_comptes_employe": "Type de compte employé", "Fin de relation ?": "Fin de relation ?", "Numero_compte": "Numéro de compte", "Compte_cle": "Clé", "Compte_Chapitre": "Chapitre", "Type_de_compte": "Type de compte", "Paie_normale": "Paie normale", "Gratification": "Gratification", "Paie_conges": "<PERSON><PERSON> congés", "EStatutMatrimonial.Marie": "<PERSON><PERSON>", "EStatutMatrimonial.Celibataire": "Célibataire", "EStatutMatrimonial.Divorce": "<PERSON><PERSON><PERSON>", "EStatutMatrimonial.Veuf": "Veuf/Veuve", "EStatutMatrimonial.Autre": "<PERSON><PERSON>", "Modifier_Informations": "Modifier mes informations", "Taches_Requises": "Tâches requises", "Liste_Taches": "Liste de tâches", "Gestion_Poste": "Gestion des postes", "Filiere_didentification": "Filière d’identification", "Poste_Responsabilite": "Poste de responsabilité", "Fonction_Parent": "Fonction parent", "Liste_competence": "Liste des compétences", "Liste_diplomes": "Liste des diplômes", "Liste_formations": "Liste des formations", "Fichier_Excel": "Fichier Excel", "Fichier_Unl_par_agence": "Fichier UNL par agence", "Fichier_Unl": "Fichier UNL", "Validation_des_calcules": "Validation des calculs", "Generation_etat_contribution": "Génération des états de contributions", "Supprimer_dernier_bulletin": "Supprimer le dernier bulletin", "Actualiser_fiche_poste": "Actualiser la fiche de poste", "Competences_Requises": "Compétences requises", "Formations_Requises": "Formations requises", "app.param_gen.fonctions": "Fonctions", "Poste_vacant": "<PERSON><PERSON> vacant", "Liste_poste_vacant": "Liste des postes vacants", "intitule_cellule_parent": "Intitulé de la cellule parent", "code_poste": "Code du poste", "intitule_poste": "Intitulé du poste", "code_cellule_parent": "Code de la cellule parent", "date_debut_sanction": "Date de début de la sanction", "date_fin_sanction": "Date de fin de la sanction", "EDataUploadType.Diplome": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "EDataUploadType.DiplomeEmploye": "Diplômes des employés", "EDataUploadType.Filiere": "Filières", "EDataUploadType.Ecole": "Écoles", "EDataUploadType.Equivalence": "Équivalence", "Nombre_jour": "Nombre de jours", "EtypeContrat.Duree_Indeterminee": "CDI", "Diplomes_Requis": "Diplômes requis", "Poste_responsabilite": "Poste de responsabilité", "EtypeContrat.Duree_Determinee": "CDD", "EtypeContrat.ESSAIE": "<PERSON><PERSON><PERSON>", "EStatutContrat.ENREGISTRE": "Enregistré", "EStatutContrat.ENCOURS": "En cours", "EStatutContrat.TERMINE": "<PERSON><PERSON><PERSON><PERSON>", "EStatutContrat.CLOTURE": "Clôturé", "EStatusConge.VALIDER": "<PERSON><PERSON><PERSON>", "EStatusConge.ANNULER": "<PERSON><PERSON><PERSON>", "EStatusConge.RETOUR": "Retour", "EStatusConge.EN_ATTENTE": "En attente", "EStatusConge.REFUSER": "<PERSON><PERSON><PERSON><PERSON>", "ESanctionStatus.ATTENTE_SANCTION": "Sanction en attente", "ESanctionStatus.VALIDER_SANCTION": "Sanction validée", "ESanctionStatus.REJECT": "<PERSON><PERSON><PERSON>", "Mension": "Mention", "afficherBulletinConge": "Afficher bulletin de congés", "Rubrique_Conge": "Rub<PERSON>que congés", "Bilan_Salariale": "Bilan salarial", "Nouvelle Norme": "Nouvelle norme", "REPORTING_REALISATION": "Reporting réalisation", "Stats_Conges": "Statistiques des congés", "Rubrique_mission": "Rubrique mission", "Frais_mission": "Frais de mission", "Node_mission": "Node mission", "Planning_mission_valide": "Planning mission validé", "LevelFormation": "Niveau de formation", "Liste_company_trainer": "Liste des entreprises formatrices", "CategorieFormation": "Catégorie de formation", "TrainingOffer": "Offre de formation", "CompanyTrainer": "Entreprise formatrice", "Type_pret": "Type de prêt", "Liste_pret": "Liste des prêts", "Type_visite": "Type de visite", "Type_examen": "Type d'examen", "ETitre.Mme": "Mme", "ETitre.M": "<PERSON>.", "ETitre.Dr": "Dr.", "ETitre.Pr": "Pr.", "Demande_Recrutement": "<PERSON><PERSON><PERSON> de recrutement", "Liste_demande_recrutement": "Liste des demandes de recrutement", "Demande_recrutement": "<PERSON><PERSON><PERSON> de recrutement", "Poste_pouvoir": "Poste à pourvoir", "Ressource_recrutement": "Ressource recrutement", "Anciennete_candidat": "Ancienneté du candidat", "Fiche_poste": "Fiche de poste", "Critere_recrutement": "Critère de recrutement", "Mensualite": "Mensualité", "Montant_restant": "<PERSON><PERSON> restant", "Etape_Parent ": "Étape parent", "Suffixe": "Suffixe", "Liste_des_elements_de_facturation": "Liste des éléments de facturation", "Authentification": "Authentification", "Authentication": "Authentification", "Bienvenue sur": "Bienvenue sur", "Connexion": "Connexion", "J'ai oublie mon MDP": "J'ai oublié mon mot de passe", "Current password": "Mot de passe actuel", "New password": "Nouveau mot de passe", "Password confirmation": "Confirmation du mot de passe", "Enter your email address and your password will be reset and emailed to you": "Entrez votre adresse e-mail, votre mot de passe sera réinitialisé et envoyé par e-mail.", "Email address": "Adresse e-mail", "French": "Français", "English": "<PERSON><PERSON><PERSON>", "J'ai pas de compte": "Je n’ai pas de compte", "M'incrire": "M'inscrire", "Sign out": "Déconnexion", "Save.failed": "Échec", "Record.not.exists": "Cette donnée n'existe plus", "Record.used.elsewhere": "Cette donnée est utilisée ailleurs", "Object.not.found": "Donnée non trouvée", "Database.integrity.violated": "Cohérence de la base de données violée", "Welcome to": "Bienvenue sur", "Qualite": "Qualité", "Login.failed": "Erreur de connexion", "Select.Valid.Exercice": "Sélectionnez un exercice valide", "Forgot password": "Mot de passe oublié", "Enter your login and your password will be reset and emailed to you": "Entrez votre identifiant, votre nouveau mot de passe vous sera envoyé par e-mail", "Send me new password": "Nouveau mot de passe", "Send me back": "Retour", "to the sign in screen.": "à la page de connexion", "Forget it,": "", "This login is incorrect.": "Votre identifiant est incorrect", "Your user account is Frozen.": "Votre compte utilisateur est gelé", "Your user account is Pending.": "Votre compte utilisateur est en attente", "Votre_Matricule": "Votre matricule", "APP_NAME": "E-HUMAN RESSOURCES", "Intitule.Null": "Intitulé vide", "Contrainte.Null": "Contrainte vide", "Type.Justificatif.Null": "Type de justificatif vide", "Type.Donnee.Null": "Type de donnée vide", "Unique.Null": "Unique", "Obligatoire.Null": "Obligatoire", "Taille.Null": "<PERSON><PERSON>", "Mois.Insertion.Eval.Null": "Mois d'insertion Évaluation vide", "Mode.Calcul.Synthese.Null": "Mode de calcul de la synthèse vide", "Saisie.Justificatif.Obligatoire.Null": "Saisie de justificatif obligatoire vide", "Principe.Verification.Justificatif.Null": "Principe de vérification du justificatif vide", "JSONDataType.INT": "INT", "JSONDataType.BOOLEAN": "BOOLEAN", "JSONDataType.LONG": "LONG", "JSONDataType.STRING": "STRING", "JSONDataType.DATE": "DATE", "JSONDataType.OBJECT": "OBJECT", "JSONDataType.ARRAY": "ARRAY", "JSONDataType.ENUM": "ENUM", "JSONDataType.DOUBLE": "DOUBLE", "Send_to_validation": "Envoyer en validation", "REST_APIS": "APIs REST", "JSON_OBJECT_DESCS": "Objet JSON", "JSON_BODY_DESCS": "Contenu JSON", "REST_DATA_SOURCES": "Sources REST", "JSONBodyDescs": "Descriptions de corps JSON", "JSONBodyDesc": "Description du corps JSON", "BUILD_FROM_JSON_STRING": "Construire à partir d'une chaîne JSON", "HEADER_PARAMS": "Paramètres en-tête", "Code": "Code", "Value": "<PERSON><PERSON>", "User_details": "Détails de l'utilisateur", "JsonObjectDesc": "Description de l'objet JSON", "JsonArrayDesc": "Description du tableau JSON", "Type": "Type", "Intitule": "Intitulé", "PRAMETRES": "Paramètres", "Ordre": "Ordre", "Origine": "Origine", "JSON Attribute": "Attribut JSON", "SELECTIONNER_UN_TYPES": "Sélectionnez un type", "Enter your JSON  String": "Entrez une chaîne JSON", "Save": "<PERSON><PERSON><PERSON><PERSON>", "Cancel": "Annuler", "JsonAttributeDesc": "Description de l'attribut JSON", "SELECT_A_JSONATTRIBUTEDESC": "Sélectionnez un attribut", "Attributes": "Attributs", "Selectionner un type": "Sélectionnez un type", "Liste-De-JSONBodyDesc": "Liste des descriptions d'objets JSON", "Designation": "Désignation", "Search": "<PERSON><PERSON><PERSON>", "DataType": "Type de donnée", "ObjectDesc": "Desc d'objet", "ArrayDesc": "Desc d'array", "Consulter": "Consulter", "Modifier": "Modifier", "Method": "Méthode", "SELECTIONNER_UNE_METHODE": "Sélectionnez une méthode", "BASE_URL": "URL de base", "PATH_PARAM": "Paramètre d'URL", "HEADER_PARAM": "Paramètre en-tête", "REQUEST_PARAM": "Paramètre de requête", "REQFIELD": "Champs requis", "SELECT_A_RESTDATASOURCE": "Sélectionnez une source de données", "Liste-Des-RestDataSource": "Liste des sources de données REST", "RestDataSource": "Source de données REST", "Details": "Détails", "TEST_API": "Tester l'API", "RESPONSE_BODY": "Corps de réponse", "REQUEST_BODY": "Corps de la requête", "CurrentMode0": "Création", "CurrentMode1": "Consultation", "CurrentMode2": "Édition", "STD_PROFILE_FONCTIONALITE": "Fonctionnalité", "STD_PROFILE_FONCTIONALITE_MIN_LEVEL": "Nive<PERSON> min", "STD_PROFILE_FONCTIONALITE_ACTION": "Action", "STD_ENABLE_ALL": "Tout <PERSON>r", "STD_DISABLE_ALL": "<PERSON><PERSON>", "Yes": "O<PERSON>", "No": "Non", "JSON_ATTRIBUTE_DESCS": "Attributs JSON", "JSON_ENUMS_DESCS": "ENUM JSON", "JSON_ARRAY_DESCS": "Liste JSON", "CODEGROUPE": "Code groupe", "ECHELON": "Échelon", "SALAIREBRUT": "Salaire brut", "SALAIREBRUTMAX": "Salaire brut max", "CODEDEVISE": "Code devise", "CodeGroupe.Null": "Le code du groupe est vide", "Echelon.Null": "L'échelon est vide", "Code.Null": "Le code est vide", "CodeDevise.Null": "Le code de la devise est vide", "SalaireBrut.Null": "Le salaire brut est vide", "SalaireBrutMax.Null": "Le salaire brut max est vide", "Code.Exist": "Code existant", "Rang": "<PERSON>ng", "Liste des grades": "Liste des grades", "Parametres generaux": "Paramètres généraux", "Home": "Accueil", "Ajout": "<PERSON><PERSON><PERSON>", "Classe": "Classe", "Liste des classes": "Liste des classes", "Infos Connexion": "Infos connexion", "Extractions": "Extractions", "Criteres/Fonction": "Critères par fonction", "Fonctions": "Fonctions", "Grade": "Grade", "Types de noeuds": "Types de nœuds", "Organigramme": "Organigramme", "Reseau": "<PERSON><PERSON><PERSON>", "REST APIS": "REST APIS", "Parametres specifiques": "Paramètres d'évaluation", "PeriodiciteEvaluations": "Périodicité d'évaluation", "Proprietes": "Propriétés", "Type justificatif": "Type justificatif", "Types Automatiques": "Types automatiques", "Nature Justificatif": "Nature du justificatif", "Critere": "Critère", "Lissage Par Agence": "Lissage par agence", "Lissage Par Employe": "Lissage par <PERSON>é", "Mon evaluation": "Mon évaluation", "Mes objectifs": "Mes objectifs", "Mes realisations": "Mes réalisations", "Raports realisations": "Rapports réalisations", "Gestion evaluation": "Gestion évaluation", "Valider des documents": "Valider des documents", "Etat global objectifs": "État global objectifs", "Etat global de realisations": "État global des réalisations", "Operations RH": "Opérations RH", "Attentes chez managers": "Attentes chez managers", "Rapports": "Rapports", "Employes": "Employés", "Personnel": "Personnel", "Gestion Document": "Gestion des documents", "Generation Document": "Génération de documents", "Administration": "Administration", "Derogations": "Dérogations", "Gestion user": "Gestion des utilisateurs", "Profiles": "Profils", "Levels": "Niveaux", "Users": "Utilisateurs", "Authorities": "Autorisations", "Sessions": "Sessions", "Languages": "<PERSON><PERSON>", "Countries": "Pays", "Envoyer": "Envoyer", "Operations manager": "Opérations manager", "Envoi En Validation": "Envoi en validation", "Rejeter": "<PERSON><PERSON><PERSON>", "Valider": "Valider", "Motif": "<PERSON><PERSON><PERSON>", "Enregistrer": "Enregistrer", "Fermer": "<PERSON><PERSON><PERSON>", "Nom": "Nom", "Prenoms": "Prénoms", "Titre": "Titre", "Sexe": "<PERSON>e", "Statut": "Statut", "Matricule": "Matricule", "Etat": "État", "Normes et Objectifs": "Normes et objectifs", "Norme": "Norme", "Normes": "Normes", "Objectifs": "Objectifs", "Objectif": "Objectif", "Libelle": "Libellé", "Destinataire": "<PERSON><PERSON><PERSON>", "Agence": "Agence", "Cellule": "Cellule", "Pays": "Pays", "pays_pluriel": "Pays", "Region": "Région", "Acte num": "Numéro d'acte de naissance", "Pere": "Père", "Mere": "<PERSON><PERSON>", "Ne le": "Date de naissance", "Du": "Date d'établissement de l'acte", "A": "Lieu de naissance", "Unite": "Unité", "User": "Utilisa<PERSON>ur", "Radie": "<PERSON><PERSON><PERSON>", "Nature": "Nature", "Fonction Principale": "Fonction principale", "Ajouter": "Ajouter", "Informations Professionnelles": "Informations professionnelles", "Managers": "Managers", "Contacts": "Contacts", "Informations Personnelles": "Informations personnelles", "Employe": "Employé", "Parametres evaluation": "Paramètres d'évaluation", "CritereParFonctions": "Critère par fonction", "Elements de controle": "Éléments de contrôle", "Liste des elements de controle": "Liste des éléments de contrôle", "Taille": "<PERSON><PERSON>", "Type de donnees": "Types de données", "Type donnee": "Type de données", "Mode de saisie": "Mode de saisie", "Exercice": "Exercice", "NbrPeriode": "Nombre de périodes", "PeriodiciteEvaluation": "Périodicité d'évaluation", "Periodes": "<PERSON><PERSON><PERSON><PERSON>", "Numero": "<PERSON><PERSON><PERSON><PERSON>", "Encours": "En cours", "Derniere": "<PERSON><PERSON><PERSON>", "Debut": "D<PERSON>but", "Fin": "Fin", "Liste-Des-PeriodiciteEvaluation": "Liste des périodicités d'évaluation", "EDataSourceType.REST": "WS REST", "EDataSourceType.DATABASE": "Base de données", "EPeriodicite.Quotidienne": "Quotidienne", "EPeriodicite.Hebdomadaire": "Hebdomadaire", "EPeriodicite.Mensuelle": "<PERSON><PERSON><PERSON>", "EPeriodicite.Trimestrielle": "Trimes<PERSON>elle", "EPeriodicite.Semestrielle": "<PERSON><PERSON><PERSON><PERSON>", "EPeriodicite.Annuelle": "<PERSON><PERSON><PERSON>", "ETypePropriete.Text": "Texte", "ETypePropriete.File": "<PERSON><PERSON><PERSON>", "ETypePropriete.Single_Select": "Sélection unique", "ETypePropriete.Date": "Date", "ETypePropriete.FormattedText": "Texte formaté", "ETypePropriete.Integer": "<PERSON><PERSON>", "ETypePropriete.Number": "Nombre", "ETypePropriete.Multiple_Select": "Sélection multiple", "LISTE_DES_TYPES_JUSTIFICATIFS": "Liste des types de justificatifs", "LISTE_DE_NATURE_JUSTIFICATIF": "Liste des natures de justificatifs", "Mode de Saisie": "Mode de saisie", "EModeSaisie.MANUEL": "<PERSON>", "EModeSaisie.AUTOMATIQUE_DB": "De la base de données", "EModeSaisie.AUTOMATIQUE_REST": "De webservices REST", "EModeSaisie.AUTOMATIQUE": "Automatique", "Unique": "Unique", "Obligatoire": "Obligatoire", "Propriete": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Mode saisie": "Mode de saisie", "TypeJustificatif": "Type de justificatif", "NatureJustificatif": "Nature du justificatif", "Data source": "Source de données", "Lier les resultats de recherche aux proprietes": "Lier les résultats de recherche aux propriétés", "TypeJustificatif Auto": "Type de justificatif automatique", "Select a typeJustificatif": "Sélectionnez un type de justificatif", "Liste de Type de justificatifs": "Liste des types de justificatifs", "LISTE_DE_CRITERE_D_EVALUATION": "Liste des critères d'évaluation", "Poids": "Poids", "Periode insertion": "Période d'insertion", "Mode de Calcul": "Fonction de calcul", "Mois Insertion": "Période d'insertion", "Contrainte": "Contrainte", "Fonction calcule": "Fonction de calcul", "Mode calcule Synthese": "Mode de calcul de la synthèse", "Periode Insertion": "Période d'insertion", "Saisie Obligatoire": "<PERSON><PERSON> obligatoire", "Indicateur": "Critère d'évaluation", "Liste de Natures": "Liste des natures", "Selectionez une nature justificatif": "Sélectionnez une nature de justificatif", "EFonctionDeCalcul.Somme": "Somme", "EFonctionDeCalcul.Decompte": "Décompte", "EFonctionDeCalcul.Moyenne": "<PERSON><PERSON><PERSON>", "EPrincipeVerifJustif.SOMME": "Somme", "EPrincipeVerifJustif.DECOMPTE": "Décompte", "EPrincipeVerifJustif.MOYENNE": "<PERSON><PERSON><PERSON>", "Prenom": "Prénom", "Fonction": "Fonction", "Prospension a realiser": "Propension à réaliser", "Prospension A Realiser": "Propension à réaliser", "Lissage par Agence": "Ajuster critère/agence", "LissageParAgences": "Ajuster critère/agence", "Recherche": "Recherche", "Document": "Document", "Normes Objectifs": "Normes", "Noms": "Noms", "Evaluation": "Évaluation", "Valeur": "<PERSON><PERSON>", "Generer realisaiton": "Générer réalisation", "Generer": "<PERSON><PERSON><PERSON><PERSON>", "PeriodeEvaluation": "Période d’évaluation", "Exporter": "Exporter", "Envoyer en validation": "Envoyer en validation", "Liste-De-Mes-Realisations": "Liste de mes réalisations", "Jusitificatifs": "Justificatifs", "Nbr_Propriete": "Nombre de propriétés", "Commentaires": "Commentaires", "Justificatif": "Justificatif", "REQUIRED_FIELDS": "Champs requis", "SEARCHED_FIELDS": "<PERSON><PERSON> recherchés", "UINIQ_FIELDS": "Champs uniques", "Imprimer": "<PERSON><PERSON><PERSON><PERSON>", "Liste des employes": "Liste des employés", "InfoConnects": "Infos connexion", "Liste des infoConnects": "Liste des connexions à la base de données", "USER_NAME": "Nom d'utilisateur", "SGBD": "SGBD", "Port": "Port", "Host": "<PERSON><PERSON><PERSON>", "HOST": "<PERSON><PERSON><PERSON>", "Password": "Mot de passe", "Username": "Nom d'utilisateur", "Database": "Base de données", "Test Connection": "<PERSON>", "InfoConnect": "Info connexion", "Liste des Extractions": "Liste des extractions", "Extraction BD": "Extraction BD", "Extractions BD": "Extractions BD", "Connexions BD": "Connexions BD", "Extraction Name": "Nom de l'extraction", "Table Name": "Nom de la table", "ColumnExtract": "Colonnes extraites", "ParamsExtra": "Paramètres d'extraction", "TableName": "Nom de la table", "Liste des attributes json": "Liste des attributs JSON", "JSONAttributeDesc": "Descriptions d'attributs JSON", "JSONEnumDesc": "Description d'ENUM JSON", "Condition WHERE": "Condition WHERE", "Liste types de noeuds": "Liste des types de nœuds", "Type de noeud": "Type de nœud", "Organigrame": "Organigramme de l'entreprise", "Noeud organigrame": "Noeud organigramme", "Employe responsable": "Employé responsable", "Fonctions disponibles": "Fonctions disponibles", "Fonctions attribuees": "Fonctions attribuées", "Noeud reseau": "<PERSON><PERSON><PERSON>", "Liste des enums json": "Liste des ENUM JSON", "JSON ENUM": "Énumération JSON", "Liste des valuers": "Liste des valeurs", "JSONObjectDesc": "Descriptions d'objet JSON", "Liste of json enums": "Liste d'objets JSON", "Liste des attributs": "Liste des attributs", "JSON Array": "Liste JSON", "Liste des json array": "Liste des tableaux JSON", "Liste des bodys json": "Résultats des requêtes HTTP", "JSON Body": "Contenu JSON de la réponse", "Liste des exercices": "Liste des exercices", "Annee": "<PERSON><PERSON>", "Liste des Criteres normes par Fonction": "Liste des critères et normes par fonction", "Fonction Criteres Normes": "Fonction Critères Normes", "Liste des fonctions": "Liste des fonctions", " Liste des Activites": "Liste des activités", "Activite": "Activité", "Periodicite": "Périodicité", "Select a activite": "Sélectionnez une activité", "Documents en attente de derogation": "Documents en attente de dérogation", "Reinitialisation des documents": "Réinitialisation des documents", "Validation des documents check": "Documents en attente de validation chez le manager", "Validation des documents Manager": "Validation des documents du manager", "Validation des documents RH": "Validation des documents RH", "Recherche employe": "Recherche employé", "Generation des documents Normes Objectifs": "Génération des documents Normes Objectifs", "Generer les Documents": "Générer les documents", "LissageParEmploye": "Ajustement critère par employé", "Situation_familiale": "Situation familiale", "Qualifications_Academiques": "Qualifications académiques", "Pieces didentite": "Pièces d'identité", "Securite sociale": "Sécurité sociale", "Table_des_diplomes": "Table des diplômes", "Mention": "Mention", "Etablissement": "Établissement", "Obtenu_a_letranger": "Obtenu à l'étranger", "Annee_obtention": "<PERSON><PERSON> d'obtention", "Tritre": "Titre", "Etablissement/Adresse": "Établissement/Adresse", "Select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Diplome": "Diplôme", "Tableau des enfants et conjoints": "Tableau des enfants et conjoints", "Date_de_Naissance": "Date de naissance", "Numero_acte": "Numéro de l'acte de naissance", "Nom Prenom": "Noms et prénoms", "Enfant": "<PERSON><PERSON>", "Piece didentification": "<PERSON><PERSON>ce d'identification", "Type de piece didentification": "Type de pièce d'identification", "Delivre le": "Expire le", "Expiration": "Expiration", "Lieu": "<PERSON><PERSON>", "Ville": "Ville", "tel-Portable": "Portable", "Adresse": "<PERSON><PERSON><PERSON>", "Domicile": "<PERSON><PERSON><PERSON>", "Bureau": "Bureau", "Email": "E-mail", "Boite postale": "Boîte postale", "Fax": "Fax", "Employeur": "Employeur", "Nom_de_jeune_fille": "Nom de jeune fille", "Confirme le": "Confirm<PERSON> le", "Embauche le": "Em<PERSON><PERSON><PERSON> le", "Tableau des fonctions": "Tableau des fonctions", "Numero-matricule": "Matricule", "TypeJustificatifsAuto": "Types de justificatifs automatiques", "NatureJustificatifs": "Natures de justificatifs", "Indicateurs": "Critères d'évaluation", "TypeJustificatifs": "Types de justificatifs", "JSONArrayDesc": "Description JSON Array", "RestDataSources": "Sources de données webservice REST", "Dupliquer": "<PERSON><PERSON><PERSON><PERSON>", "Ajouter_la_fonction": "Entrer la fonction", "Entrer_le_sexe": "Entrer le sexe", "Entrer_la_date_fin_d'embauche": "Entrer la date de fin d'embauche", "Entrer_la_date_embauche": "Entrer la date d'embauche", "Date_embauche_superieur_a_la_date_confirmation": "La date d'embauche est supérieure à la date de confirmation", "Date_fin_inferieur_a_la_date_embauche": "La date de fin d'embauche est antérieure à la date d'embauche", "Date_fin_inferieur_a_la_date_confirmation": "La date de fin d'embauche est antérieure à la date de confirmation", "Compte_utilisateur_deja_utilise": "Compte utilisateur déjà attribué", "Date_fin_d'embauche_posterieur": "La date de fin d'embauche est supérieure à la date actuelle", "Date_embauche_posterieur": "La date d'embauche est supérieure à la date actuelle", "Date_confirmation_posterieur": "La date de confirmation est supérieure à la date actuelle", "DateActeNaissance_posterieur": "La date d'acte est supérieure à la date actuelle", "DateCnps_posterieur": "La date de sécurité sociale est supérieure à la date actuelle", "Date_posterieur": "La date de naissance est supérieure à la date actuelle", "Entrer_la_date_de_naissance": "Entrer la date de naissance", "Objectif_inferieur_ï¿½_la_norme": "L'objectif doit être supérieur à la norme", "Devise": "<PERSON><PERSON>", "Devises": "Devi<PERSON>", "CodeNumerique.Exist": "Le code numérique existe déjà", "RaportsRealisations": "Rapports des réalisations", "Generer rapport": "Générer un rapport", "PeriodeMin": "<PERSON><PERSON><PERSON><PERSON> min", "PeriodeMax": "Période max", "Pardefaut": "<PERSON><PERSON> <PERSON><PERSON>", "Zone monetaire": "Zone monétaire", "Symbol": "Symbole", "Code Numerique": "Code numérique", "Devise par defaut": "<PERSON><PERSON> par défaut", "Definir comme devise par defaut?": "Définir comme devise par défaut ?", "STD_NO": "Non", "STD_YES": "O<PERSON>", "Supprimer": "<PERSON><PERSON><PERSON><PERSON>", "Update.succesful": "Mise à jour réussie", "STD_OPERATION_SUCCESSFUL": "Opération réussie", "Create.succesful": "<PERSON><PERSON><PERSON> avec succès", "STD_DELETE": "Suppression", "Liste des devises": "Liste des devises", "EModeCalculSynthese.Cumul": "Cumul", "EModeCalculSynthese.Moyenne": "<PERSON><PERSON><PERSON>", "STD_ADD": "Ajouter", "STD_EDIT": "Modifier", "NO_SELECTION": "Aucune sélection", "No data found": "<PERSON><PERSON><PERSON> donnée disponible", "STD_OPERATION_FAILED": "Échec de l'opération", "CLOSE": "<PERSON><PERSON><PERSON>", "OK": "OK", "c": "Rejeté par le manager", "EtatDocument.VALIDERRH": "Validé par la DRH", "EtatDocument.VALIDER": "Validé par le manager", "EtatDocument.ENCOURS": "En attente de validation du manager", "EtatDocument.ENREGISTRER": "Enregistré", "EtatDocument.DEROGATION_VALIDATION": "En dérogation de validation", "EtatDocument.DEROGATION_REJET": "En dérogation de rejet", "EtatDocument.TRANSFERER": "Transféré par la DRH", "EtatDocument.REJETDERH": "Rejeté par la DRH", "EtatDocument.DEROGATION": "En dérogation", "REJETER": "Rejeté par le manager", "VALIDERRH": "Validé par la DRH", "VALIDER": "Validé par le manager", "ENCOURS": "En cours", "ENREGISTRER": "Enregistré", "DEROGATION_VALIDATION": "En dérogation de validation", "DEROGATION_REJET": "En dérogation de rejet", "TRANSFERER": "Validé par la DRH", "REJETDERH": "Rejeté par la DRH", "DEROGATION": "En dérogation", "EMPLOYES RADIES": "Employés radiés", "EMPLOYES": "Employés", "DOCUMENTS NORMES GENERES": "Documents normes générés", "DOCUMENTS OBJECTIFS VALIDES": "Documents objectifs validés", "Employe & Configurations Generales": "Employé & Configurations générales", "Dashboard": "Tableau de bord", "Bienvenue": "Bienvenue", "Logout": "Déconnexion", "Account": "<PERSON><PERSON><PERSON>", "Login failed": "Erreur de connexion", "LOGIN_ACCOUNT_LOCKED": "<PERSON>mpte suspendu", "LOGIN_ACCOUNT_LOCKED_TITLE": "Erreur de connexion", "LOGIN_ACCOUNT_EXPIRED": "Compte expiré", "LOGIN_ACCOUNT_EXPIRED_TITLE": "Erreur de connexion", "LOGIN_ACCOUNT_INCORRECT": "Veuillez vérifier le nom d'utilisateur et le mot de passe", "LOGIN_ACCOUNT_INCORRECT_TITLE": "Erreur de connexion", "LOGIN_AUTH_ERROR": "Une erreur est survenue lors de la connexion", "LOGIN_AUTH_ERROR_TITLE": "Erreur de connexion", "Profile.pwdMinLength.Null": "Longueur minimale du mot de passe vide", "Profile.defaultPwd.Null": "Mot de passe par défaut vide", "Profile.uNameDuration.Null": "Durée en jours du nom d'utilisateur vide", "Profile.pwdDuration.Null": "Durée en jours du mot de passe vide", "Profile.type.Null": "Type de profil vide", "Profile.code.Null": "Code du profil vide", "Profile.id.Null": "ID vide", "ChangePassword.Old.Invalid": "Ancien mot de passe invalide", "Password.Format.Length.Less": "Mot de passe trop court", "ChangePassword.New.Same.Old": "Ancien mot de passe égal au nouveau", "ChangePassword.New.Equal.Old": "Ancien mot de passe égal au nouveau", "NewPassword.Does.Not.Match": "Votre nouveau mot de passe ne correspond pas", "ChangePassword.Succesful": "Mot de passe changé avec succès", "PASSWORD_CHANGED": "Mot de passe changé avec succès. Si vous n'avez pas reçu votre mot de passe par défaut par e-mail, veuillez contacter votre administrateur.", "You are about to change your password": "Changement du mot de passe", "You were assigned a default password, please change it to continue": "Un mot de passe par défaut vous a été assigné. Veuillez le changer", "Change password": "Changer le mot de passe", "Change default password": "Changer le mot de passe par défaut", "Password expired": "Mot de passe expiré", "Your password has expired, please change it to continue": "Votre mot de passe a expiré. Veuillez le changer", "PERIODICITE_VADIDE": "Périodicité validée", "PeriodeEvaluation.NotFound": "Période d'évaluation introuvable", "Exercice.null": "Exercice vide", "Periodicite.NotValidated": "Périodicité d'évaluation non validée", "Employe.NotFound": "Employé introuvable", "DocRealisation.NotCreated": "Document de réalisation non créé", "DocRealisation.exist": "Document de réalisation existe déjà", "DNO_EVOIRE_SUP_VAL_DAYS_BETWEEN_28": "La date d'envoi doit être inférieure à la date de validation et comprise entre 1 et 28", "Date butoire evaluation": "Dates butoirs d'évaluation", "Documents normes et objectifs": "Documents des normes et objectifs", "Documents realisations": "Documents des réalisations", "Date butoire envoi en validation": "Envoi en validation", "Date butoire validation manager": "Validation manager", "Envoi en validation, Validation": "Envoi en validation et validation", "Types de noeud": "Types de nœud", "Code des types de noeud": "Code des types de nœud", "FTP": "FTP", "Parametres du serveur FTP": "Paramètres du serveur FTP", "Code type de noeud agence": "Code type de nœud agence", "Description": "Description", "Personel non grade": "Personnel non gradé", "Code grade": "Code grade", "User type cannot have admin profile": "Le type utilisateur 'User' ne peut avoir un profil 'Admin'", "Ivalid profile type": "Type de profil invalide", "Parametre introuvable": "Paramètre introuvable", "EProfileType.Admin": "Administrateur", "EProfileType.HR": "RH", "EProfileType.User": "Utilisa<PERSON>ur", "EProfileType.Root": "Root", "DerrogerDocumentObjectifs": "<PERSON><PERSON><PERSON>er les documents d'objectifs", "DerrogerDocumentRealisation": "Déroger les documents de réalisations", "ReinitialiserDocument": "Réinitialiser les documents", "DeleteUser": "Supprimer un utilisateur", "DeleteProfile": "Supprimer un profil", "EXERCICE_CREATE_EDIT": "Créer et modifier l'exercice", "EXERCICE_START": "Démarrer un exercice", "EXERCICE_STOP": "Arrêter un exercice", "EXERCICE_END": "Clôturer un exercice", "EXERCICE_DEL": "Supprimer un exercice", "Configuration": "Configuration", "KO": "KO", "Configuration Fonction": "Configuration des fonctions", "Configuration Critere": "Configuration des critères", "Attribution des criteres aux fonctions": "Attribution des critères aux fonctions", "Attribution des normes aux criteres": "Attribution des normes aux critères", "Definition Periodicite Evaluation": "Définition de la périodicité d'évaluation", "Definition Nature Justificatifs": "Définition des natures de justificatifs", "GenerationDocument": "Génération des normes et objectifs", "Tranche Age": "Tranche d'âge", "TrancheAges": "Tranches d'âge", "SaisieJustifObligPourCritere": "Saisie des justificatifs obligatoires", "Valeur Realisation": "Valeur réalisation", "Classe.Exist.In.List": "", "Liste des TypeRubClasse": "Liste des types de rubriques de classe", "TypeRubClasse": "Type de rubrique de classe", "TypeRubClasses": "Types de rubriques de classe", "Parametrage": "Paramétrage", "Paie": "<PERSON><PERSON>", "ETypeRubrique.Salaire": "Salaire", "ETypeRubrique.Plagaire": "Plagiaire", "ETypeRubrique.PlagiaireProgressif": "Plagiaire progressif", "ETypeRubrique.Classe": "Classe", "ETypeRubrique.Fonction": "Fonction", "ETypeRubrique.Grade": "Grade", "ETypeRubrique.Regional": "Régional", "ETypeRubrique.HeureSuplementaire": "Heure supplémentaire", "ETypeRubrique.Constante": "<PERSON><PERSON> constante", "ETypeRubrique.Autre": "<PERSON><PERSON>", "ETypeRubrique.Vehicule": "Véhicule", "ETypeRubrique.Credit": "Crédit", "ETypeRubrique.Conge": "<PERSON><PERSON>", "ETypeRubrique.Absence": "Absence", "ETypeRubrique.Cotisation": "Cotisation", "ETypeRubrique.NonImposable": "Non imposable", "ETypeRubrique.Separation": "Séparation", "TypeRubFonction": "Type de rubrique fonction", "TypeRubFonctions": "Types de rubriques fonction", "Liste des TypeRubGade": "Liste des types de rubriques grade", "TypeRubGade": "Type de rubrique grade", "TypeRubGades": "Types de rubriques grade", "Interval.Exist": "L'intervalle existe", "Interval.Limits.Touches.Another": "La limite d'intervalle touche une autre", "Interval.Crosses.Another.At.Left": "L'intervalle croise un autre à gauche", "Interval.Crosses.Another.At.Right": "L'intervalle croise un autre à droite", "Interval.In.Another": "Intervalle dans un autre", "Interval.Englobing.Another": "Intervalle englobant un autre", "Fonction.Exist.In.List": "La fonction existe dans la liste", "Grade.Exist.In.List": "Le grade existe dans la liste", "Liste des TypeRubImpotSurRevenue": "Liste des types de rubriques impôt sur revenu", "TypeRubImpotSurRevenue": "Type de rubrique impôt sur revenu", "TypeRubImpotSurRevenues": "Types de rubriques impôt sur revenu", "IMPOT_SUR_REVENUE": "Impôt sur revenu", "Taux": "<PERSON><PERSON>", "Min": "Min", "Max": "Max", "Ajouter a la liste": "Ajouter à la liste", "Liste des TypeRubPlagaireSalaireBase": "Liste des types de rubriques plagiaires salaire de base", "TypeRubPlagaireSalaireBase": "Type de rubrique plagiaire salaire de base", "TypeRubPlagaireSalaireBases": "Types de rubriques plagiaires salaire de base", "PLAGAIRE_SALAIRE_BASE": "Plagiaire salaire de base", "Liste des TypeRubPlagaireSalTaxable": "Liste des types de rubriques plagiaires salaire taxable", "TypeRubPlagaireSalTaxable": "Type de rubrique plagiaire salaire taxable", "TypeRubPlagaireSalTaxables": "Types de rubriques plagiaires salaire taxable", "PLAGAIRE_SAL_TAXABLE": "Plagiaire salaire taxable", "Liste des TypeRubSurtaxProgressive": "Liste des types de rubriques surtaxe progressive", "TypeRubSurtaxProgressive": "Type de rubrique surtaxe progressive", "TypeRubSurtaxProgressives": "Types de rubriques surtaxe progressive", "SURTAX_PROGRESSIVE": "Surtaxe progressive", "Min.Greater.Than.Max": "Min supérieure à Max", "Types rubrique": "Types de rubrique", "Niveau": "Niveau", "Rubrique": "Rubrique", "Rubriques": "Rubriques", "Liste des Rubrique": "Liste des rubriques", "Court": "Court", "prorata": "Prorata", "chargePatronale": "Charge patronale", "impotPartiel": "Imposition partielle", "appCumulConge": "Appliquer au cumul congé", "ajoutAuSalInterTaxable": "Ajouter au salaire intermédiaire taxable", "ajoutAuSalaireBrutCotisable": "A<PERSON>ter au salaire brut cotisable", "sousAuRevenueGlobalNet": "Soustraire du revenu global net", "sousImpotRevenu": "Soustraire de l'impôt sur revenu", "afficheCumulBulPaie": "Afficher le cumul sur le bulletin de paie", "passeCompteLiason": "Passer sur le compte de liaison", "consrvCumulFinExercice": "Conserver le cumul en fin d'exercice", "appCumulPaieNormale": "Affiche<PERSON> le cumul depuis l'embauche", "appCumulPaieGratification": "Appliquer au cumul des gratifications", "ajoutRevenuBrutGlobal": "Ajouter au revenu brut global", "No description found": "Aucune description", "User.Profile.Null": "Profil requis", "User.Language.Null": "Langue requise", "User.Type.Null": "Type d'utilisateur requis", "User.Email.Invalid": "E-mail invalide", "User.Login.Null": "Identifiant requis", "EStatus.Pending": "En attente", "EStatus.Active": "Actif", "EStatus.Suspended": "Suspendu", "EStatus.Frozen": "<PERSON><PERSON><PERSON><PERSON>", "EUserType.User": "Utilisa<PERSON>ur", "EUserType.Admin": "Administrateur", "EUserType.System": "Système", "Validation.failed": "Erreur de validation", "STD_DELETECONFIRMATION": "Voulez-vous supprimer ?", "EWriteMode.ltr": "Gauche à droite", "EWriteMode.rtl": "Droite à gauche", "Locale": "Locale", "Active": "Actif", "Prefered": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Flag": "<PERSON><PERSON><PERSON>", "update entity sucess 3003": "Mise à jour avec succès", "Max. sessions": "Sessions max", "Max. attempts": "Essaies de connexion max", "Default password": "Mot de passe par défaut", "User name validity": "Validité du login", "Password validity": "Validité du mot de passe", "Min uppercase chars in pwd.": "Nombre min. de majuscules", "Min pwd. length": "Longueur min. du mot de passe", "Min numeric chars in pwd.": "Nombre min. de chiffres", "Min special chars in pwd.": "Nombre min. de caractères spéciaux", "Self created": "<PERSON><PERSON><PERSON> son compte", "Auto validated": "Validation automatique", "Send mail": "Envoyer un e-mail", "Login": "Nom d'utilisateur", "Status": "Statut", "Login validity": "Validité du login", "ERubriqueAppliquerSur.SalaireBase": "Salaire de base", "ERubriqueAppliquerSur.SalaireBrut": "Salaire brut", "ERubriqueAppliquerSur.SalaireBrutCotisable": "Salaire brut cotisable", "ERubriqueAppliquerSur.SalaireBrutTaxable": "Salaire brut taxable", "ERubriqueAppliquerSur.Rubrique": "Rubrique", "Plagaire": "Plagiaire", "PlagiaireProgressif": "Plagiaire progressif", "AppliqueSur": "Appliquer sur", "Type_Sanction": "Type de sanction", "mise_a_pied": "Mise à pied", "Nombre_de_Jours_Mise_a_pied": "Nombre de jours de mise à pied", "Liste_des_type_de_sanctions": "Liste des types de sanctions", "Penalisante": "Pénalisante ?", "Duree": "<PERSON><PERSON><PERSON>", "Liste_type_notes_disciplinaires": "Liste des types de notes disciplinaires", "Delai_reponse": "<PERSON><PERSON><PERSON> (jours)", "Type_Note_Disciplinaire": "Type de note disciplinaire", "Liste_fautes": "Liste des fautes", "Faute": "<PERSON><PERSON><PERSON>", "Liste_Notes_Disciplinaires": "Liste des notes disciplinaires", "Date_Emission": "Date d'émission", "Date_Delai_Reponse": "Date du délai de r<PERSON>po<PERSON>", "Emetteur": "<PERSON><PERSON><PERSON>", "Liste_Absences": "Liste des absences", "Note_Disciplinaire": "Note disciplinaire", "Pièces_Justificatives": "Pièces justificatives", "Reponse_Employe": "Réponse employé", "Sanctions": "Sanctions", "Motif_rejet": "<PERSON><PERSON><PERSON>", "Validate_Sanction": "Valider sanction", "Reject_Sanction": "<PERSON><PERSON>er sanction", "Liste_demandes_Conges": "Liste des demandes de congés", "Type_Mission": "Type de mission", "Date_Demande": "Date de la demande", "Date_Debut": "Date de début", "Date_Fin": "Date de fin", "Demande_Mission": "<PERSON><PERSON><PERSON> de <PERSON>", "Nombre_Jours": "Nombre de jours", "Commentaire": "Commentaire", "Liste_Frais_Missions": "Liste des frais de mission", "Type_Attribution": "Type d'attribution", "Couleur": "<PERSON><PERSON><PERSON>", "Frais_Mission": "Frais de mission", "Attribution_Frais_Mission": "Attribution de frais de mission", "Montant": "<PERSON><PERSON>", "Liste_Configurations_Missions": "Liste des configurations de mission", "Delai_Demande": "<PERSON><PERSON><PERSON>", "Prorata": "Prorata ?", "Configuration_Mission": "Configuration de la mission", "Delai_avant_demande": "<PERSON><PERSON><PERSON> avant la demande", "Liste_retours_effectifs_mission": "Liste des retours effectifs de mission", "Date_Retour_Effectif": "Date de retour effectif", "Ecart": "<PERSON><PERSON><PERSON>", "Informations_Retour_Effectif_Mission": "Informations retour effectif de mission", "Date_Fin_Previsionelle": "Date de fin prévisionnelle", "Retour_Effectif": "Retour effectif", "Liste_des_Rubriques": "Liste des rubriques", "Liste_type_conges": "Liste des types de congés", "Type_Conge": "Type d'absence", "Conge_Annuel": "Congé annuel ?", "Statistiques_conges": "Statistiques des congés", "Etat_Conge": "État du congé", "Liste_retours_effectifs_conges": "Liste des retours effectifs de congés", "Type_Absence": "Type d'absence", "Informations_Retour_Effectif": "Informations retour effectif", "Planning_conges_valides": "Planning des congés validés", "Planning_previsionnel_conges": "Planning prévisionnel des congés annuels", "Liste_Configurations_Conges": "Liste des configurations des absences", "Conge_Paye": "<PERSON><PERSON> payé", "Nbr_Max_Attribution": "Nombre max d'attributions", "Configuration_Conge": "Configuration absence", "Attribution_Nombre_Jours": "Attribution du nombre de jours", "Attribution_Nombre_Jour_Employe": "Attribution du nombre de jours", "Attribution_nombre_jour_travail": "Attribution du nombre de jours de travail", "Nombre_Jours_Accorde": "Nombre de jours accordés", "Soustraction_nombre_jours_travail": "Soustraction du nombre de jours de travail", "Fractionnement_Autorise": "Fractionnement autorisé", "Donne_Bulletin_Conge": "Donne lieu à un bulletin de congé", "Demande_Conge": "De<PERSON><PERSON> de congés/absences", "Informations_aide_decision": "Informations d'aide à la décision", "Nombre_Fractionnement_Autorises": "Nombre de fractionnements autorisés", "Nombre_Fractionnement_Restants": "Nombre de fractionnements restants", "Nombre_Jours_Autorisés": "Nombre de jours autorisés", "Nombre_Jours_Restants": "Nombre de jours restants", "Liste_des_Connexions": "Liste des connexions", "Consulter_Employes": "Consulter employés", "Consulter_liste": "Consulter liste", "EMPLOYES_RADIES": "EMPLOYÉS RADIÉS", "DOCUMENTS_NORMES_GENERES": "Documents normes générés", "Conges_Annuels": "Congés annuels", "Absences": "Absences", "CONGES_ANNUELS_EN_COURS": "Congé(s) annuels en cours", "CONGES_REJETES": "<PERSON><PERSON>(s) rejet<PERSON>(s)", "Consulter_Documents": "Consulter documents", "Nombre_employes_Agence": "Nombre d'employés par agence", "Nombre_employe_sexe": "Nombre d'employés par sexe", "Nombre_Absences_En_Cours_par_Type": "Nombre d'absences en cours par type", "Nombre_Absences_Octroyes_par_Type": "Nombre d'absences octroyées par type", "Nombre_Absences_Octroyes_par_Agence": "Nombre d'absences octroyées par agence", "Employe_Configurations_Generales": "Employé & configurations générales", "Nombre_Employes": "Nombre d'employés", "Age_Moyen": "<PERSON><PERSON> moyen", "Status_demande": "Statut de la demande", "Type_absence": "Type d'absence", "Liste_absences": "Liste des demandes d'absence", "Nombre_jours": "Nombre de jours", "Liste_retours_effectifs_congés": "Liste des retours effectifs", "Hommes": "<PERSON><PERSON><PERSON>", "Femmes": "<PERSON><PERSON><PERSON>", "Matricule_employe": "Matricule de l’employé", "Experience_Moyen": "Expérience moyenne (années)", "Config": "Config", "Configuration_Fonction": "Configuration fonction", "Configuration_Critere": "Configuration critère", "Attribution_des_criteres_aux_fonctions": "Attribution des critères aux fonctions", "Attribution_des_normes_aux_criteres": "Attribution des normes aux critères", "Definition_Periodicite_Evaluation": "Définition périodicité évaluation", "Definition_Nature_Justificatifs": "Définition nature justificatifs", "Mission_en_cours": "Mission en cours", "EConditionOperator.EQUAL": "=", "EConditionOperator.LESS_THAN": "<", "EConditionOperator.LESS_THAN_EQUAL": "<=", "EConditionOperator.GREATER_THAN": ">", "EConditionOperator.GREATER_THAN_EQUAL": ">=", "EConditionOperator.IS_NULL": "Est nul", "EConditionOperator.IS_ZERO": "<PERSON><PERSON>", "ETypeCondition.If": "Si", "ETypeCondition.IfElse": "Si <PERSON>", "ETypeCondition.IfElseIf": "Si Sinon Si", "EConditionAndOr.Return": "<PERSON><PERSON><PERSON>", "EConditionAndOr.And": "Et", "EConditionAndOr.Or": "Ou", "ENatureCaulculeRubrique.Valeur": "Nombre Base Taux", "ENatureCaulculeRubrique.Test": "Test", "ENatureCaulculeRubrique.Classe": "Classe", "ENatureCaulculeRubrique.Fonction": "Fonction employé", "ENatureCaulculeRubrique.Grade": "Grade", "ENatureCaulculeRubrique.Regional": "Régional", "ENatureCaulculeRubrique.Plagiaire": "Plagiaire", "ENatureCaulculeRubrique.PlagiaireProgressif": "Plage progressive", "ENatureCaulculeRubrique.Credit": "Crédit", "ENatureCaulculeRubrique.Conge": "<PERSON><PERSON>", "ENatureCaulculeRubrique.Absence": "Absence", "ENatureCaulculeRubrique.Autre": "<PERSON><PERSON>", "ENatureCaulculeRubrique.Somme": "Somme", "ENatureCaulculeRubrique.Calcule": "Calcul", "ENatureCaulculeRubrique.IRPP": "Impôt sur le revenu", "EFormuleRubrique.BASE": "Base", "EFormuleRubrique.NOMBRE_M_BASE": "Nombre x Base", "EFormuleRubrique.NOMBRE_M_TAUX": "Nombre x Taux", "EFormuleRubrique.NOMBRE_M_BASE_M_TAUX": "Nombre x Base x Taux", "EFormuleRubrique.BASE_M_TAUX": "Base x Taux", "EFormuleRubrique.NOMBRE_D_TAUX": "Nombre / Taux", "EFormuleRubrique.NOMBRE_D_BASE": "Nombre / Base", "EFormuleRubrique.NOMBRE_D_BASE_D_TAUX": "Nombre / Base / Taux", "EFormuleRubrique.BASE_D_TAUX": "Base / Taux", "EFormuleRubrique.NOMBRE_M_BASE_D_TAUX": "Nombre x Base / Taux", "EFormuleRubrique.TAUX_D_BASE": "Taux / Base", "EFormuleRubrique.TAUX_D_NOMBRE": "Taux / Nombre", "EFormuleRubrique.TAUX_D_NOMBRE_D_BASE": "Taux / Nombre / Base", "EFormuleRubrique.TAUX_D_NOMBRE_M_BASE": "Taux / Nombre x Base", "EOperator.BEGIN": "=", "EOperator.ADDITION": "+", "EOperator.SUBTRACTION": "-", "EOperator.MULTIPLICATION": "x", "EOperator.DIVISION": "/", "EOperator.END": ";", "EOperator.OPEN_BRACKET": "(", "EOperator.CLOSE_BRACKET": ")", "EOperator.OPEN_BRACKET_ADD": "+(", "EOperator.OPEN_BRACKET_SUB": "-(", "EOperator.OPEN_BRACKET_DIV": "/(", "EOperator.OPEN_BRACKET_MULT": "x(", "EOperator.CLOSE_BRACKET_ADD": ")+", "EOperator.CLOSE_BRACKET_SUB": ")-", "EOperator.CLOSE_BRACKET_DIV": ")/", "EOperator.CLOSE_BRACKET_MULT": ")x", "ESenseRetenue.Retenue": "Retenue", "ESenseRetenue.Gain": "<PERSON><PERSON>", "ETypeCompteImposition.AUCUN": "Aucun", "ETypeCompteImposition.COMPTE_CLIENT": "<PERSON><PERSON><PERSON>", "ETypeCompteImposition.COMPTE_EMPLOYEUR": "<PERSON><PERSON><PERSON>", "ETypeElementPaie.Constante": "<PERSON><PERSON><PERSON>", "ETypeElementPaie.FontionPaie": "Fonction de paie", "ETypeElementPaie.Rubrique": "Rubrique", "EEtatRubrique.Actif": "Actif", "EEtatRubrique.Desactive": "Inactif", "ENatureRubrique.SalaireBase": "Salaire de base", "ENatureRubrique.SalaireBrutImposable": "Salaire brut imposable", "ENatureRubrique.SalaireBrutNonImposable": "Salaire brut non imposable", "ENatureRubrique.HeureSup": "Heures supplémentaire", "ENatureRubrique.Salaire": "Salaire", "ENatureRubrique.SalaireBrutGlobale": "Salaire brut global", "ENatureRubrique.SalaireNetImposable": "Salaire net imposable", "ENatureRubrique.IR": "Impôt sur le revenu", "ENatureRubrique.SalaireNet": "Salaire net", "ENatureRubrique.Cotisation": "Contribution", "ENatureRubrique.Autre": "<PERSON><PERSON>", "auProrataHeureJour": "Au prorata heure/jour", "passerSurCompteLiason": "Passer sur compte liaison", "estChargePatronale": "Est charge patronale", "contrainteAnneeExperience": "Restriction année d'expérience", "anneeExperienceMin": "Nombre min.", "anneeExperienceMax": "Nombre max.", "contrainteAge": "Contrainte sur l'âge", "ageMin": "Âge min.", "ageMax": "Âge max.", "attributionTemporaire": "Attribution temporaire", "debutAttribution": "Date de début", "finAttribution": "Date de fin", "taxable": "Taxable", "Nature Calcule": "Nature du calcul", "SenseRetenue": "Gain/Retenue", "GroupeRubrique": "Groupe", "Plancher": "<PERSON><PERSON>", "Plafond": "Plafond", "tauxImposable": "Taux imposable", "tauxEmploye": "<PERSON><PERSON>", "tauxPatronale": "<PERSON><PERSON> patronal", "Basedecalcule": "Base de calcul", "EGroupeRubrique.Affichage": "Libellé groupe", "EGroupeRubrique.Total": "Total", "libeleTotal": "Total titre", "RubriqueTotal": "Rubrique total", "afficheEntete": "Affiche<PERSON> en en-tête", "afficheTotal": "Afficher dans total", "Groupe": "Groupe", "CumuleRubrique": "Cumul rubrique", "PayFunction": "Fonction de paie", "FonctionPaie": "Fonction de paie", "Fonction de paie": "Fonction de paie", "FonctionsPaie": "Fonctions de paie", "ReturnTypes": "Types de retours", "ReturnType": "Type de retour", "EEvalFuctionType.Calculation": "Calcul", "EEvalFuctionType.Condition": "Condition", "EEvalFuctionType.Constant": "<PERSON><PERSON><PERSON>", "EEvalFuctionType.Range": "Plage/Tranche", "EEvalFuctionType.ProgressiveRange": "Plage/Tranche progressive", "EEvalFuctionType.Rubrique": "Rubrique", "EEvalFuctionType.Classe": "Valeur par classe", "EEvalFuctionType.Fonction": "Valeur par fonction", "EEvalFuctionType.Grade": "Valeur par grade", "EEvalFuctionType.Regional": "Valeur par région", "EEvalFuctionType.SQL": "SQL", "EEvalFuctionType.EntityFeild": "Attribut d'une table", "EEvalFuctionType.Employe": "Valeur par <PERSON>é", "EEvalFuctionType.SumGain": "Somme des gains", "EEvalFuctionType.SumGainImposable": "Somme des gains imposables", "EEvalFuctionType.SumGainNonImposable": "Somme des gains non imposables", "EEvalFuctionType.SumRetenue": "Somme des retenues", "EEvalFuctionType.PeriodBased": "Basé sur les paies précédentes", "EEvalFuctionType.IR": "Impôt sur le revenu", "EEvalFuctionType.StatutEmploye": "Valeur par statut employé", "EDataType.Boolean": "Booléen", "EDataType.Integer": "<PERSON><PERSON>", "EDataType.Long": "<PERSON><PERSON> long", "EDataType.Double": "Nombre", "EDataType.BigDecimal": "Grand décimal", "EDataType.Character": "<PERSON><PERSON><PERSON>", "EDataType.String": "<PERSON><PERSON>ne de caractères", "EDataType.Date": "Date", "ETypeOperationPlage.PercentageOfBase": "% de la base", "ETypeOperationPlage.PercentageOfGap": "% de l'écart", "ETypeOperationPlage.PercentageOfDifference": "% de la différence", "ETypeOperationPlage.PercentageOfValue": "% de la valeur", "ETypeOperationPlage.Value": "<PERSON><PERSON>", "ETypeOperationPlage.Rate": "<PERSON><PERSON>", "EMois.JANVIER": "JANVIER", "EMois.FEVRIER": "FÉVRIER", "EMois.MARS": "MARS", "EMois.AVRIL": "AVRIL", "EMois.MAI": "MAI", "EMois.JUIN": "JUIN", "EMois.JUILLET": "JUILLET", "EMois.AOUT": "AOÛT", "EMois.SEPTEMBRE": "SEPTEMBRE", "EMois.OCTOBRE": "OCTOBRE", "EMois.NOVEMBRE": "NOVEMBRE", "EMois.DECEMBRE": "DÉCEMBRE", "Payslip": "Bulletin de paie", "rubriqueErreur": "Rubrique en erreur", "Liste des bulletins": "Liste des bulletins", "LISTE_DE_SECTIONS": "Liste de rubriques", "LISTE_DE_RUBRIQUES": "Liste de rubriques", "Nombre": "Nombre", "Retenue": "Retenue", "Gain": "<PERSON><PERSON>", "montantImposable": "Imposable", "NonImposable": "Non imposable", "MontantPatronale": "Patronal", "nbJourEmploye": "Jours ouvrés", "nbJourSystem": "Nombre de jours requis", "calculeAuProrataJour": "Au prorata jours", "EstCalcule": "Cal<PERSON>l ?", "ENatureCumuleRubrique.Rubrique": "Rubrique", "ENatureCumuleRubrique.FontionPaie": "Fonction de paie", "ShortName": "Court", "Appliquer": "Appliquer", "ATTRIBUER_VALEUR_AUX_EMPLOYE": "Liste des valeurs par employé", "ENaturePaieInverse.SalaireNet": "Salaire net", "ENaturePaieInverse.SalaireBase": "Salaire de base", "Date_de_naissance_enfant_invalide": "Date de naissance de l'enfant invalide", "Date_delivrance_de_piece_invalide": "Date de délivrance de la pièce invalide", "Date_delivrance_de_piece_superieur_a_date_expiraion": "Date de délivrance de la pièce supérieure à la date d'expiration", "Piece_indentite_expire": "Pièce d'identité expirée", "Duree_Indeterminee": "CDI", "Duree_Determinee": "CDD", "ESSAIE": "ESSAI", "Record.deleted": "Enregistrement supprimé", "Par_defaut_la_valeur_min_de_la_classe": "<PERSON>r d<PERSON><PERSON>, la valeur min de la classe", "Embauche_le": "Date d'embauche", "Confirme_le": "Date de confirmation", "Issued on": "Déliv<PERSON>e le", "System.error": "Une erreur système s'est produite. Veuillez contacter votre administrateur système.", "Database.Integrity.Violated": "Contrainte d'intégrité violée dans la base de données", "Affection.Select.Type": "Veuillez sélectionner le type d'affectation", "Affection.Unite.Required": "L'élément unité est requis", "Affection.Cellule.Required": "L'élément cellule est requis", "Affection.Classe.SalaireBase.Required": "Le salaire de base est requis pour l'élément classe", "Affection.Classe.Required": "L'élément classe est requis", "Affection.Grade.Required": "L'élément grade est requis", "Affection.Agence.Required": "L'élément agence est requis", "Compte.Exist": "Compte existant", "Enfant_Conjoint": "Enfant/Conjoint", "Type_de_piece": "Type de pièce ID", "ML_Type_de_piece": "Type de pièce d'identification", "liste_type_piece_id": "Types de pièces d'identification", "numcode": "Code numérique", "Phonecode": "Indicatif téléphonique", "country_code_iso2": "Code pays ISO2", "country_code_iso3": "Code pays ISO3", "Pays.Iso.Null": "Code pays ISO2 vide", "numeroActeNaissance_exist": "Numéro d'acte de naissance existant", "matriculeCnps_exist": "Numéro de sécurité sociale existant", "DipeMagnetique": "Génération des dipes magnétiques", "brutSurExercice": "Brut sur l'exercice", "cumulSommeNonImposable": "Cumul du salaire non imposable", "cumulSalaireNet": "Cumul du salaire net", "mois": "<PERSON><PERSON>", "masseSalariale": "Masse salariale", "effectif": "Effectif", "sommeSalaireNet": "Somme du salaire net", "retenuePatronale": "Retenue patronale", "SalaireBase": "Salaire de base", "annesExperience": "Années d'expérience", "nombrePoints": "Points", "numeroCompte": "Numéro de compte", "modePaiement": "Mode de paiement", "SalaireBrut": "Salaire brut", "salaireBase": "Salaire de base", "salaireNet": "Salaire net", "paieConge": "<PERSON><PERSON> con<PERSON>", "salaireBrut": "Salaire brut", "ancienSalaireBase": "Ancien salaire de base", "ancienSalaireBrut": "Ancien salaire brut", "nombreJourTravailMensuel": "Nombre de jours de travail mensuel", "nombreEnfantMaxACharge": "Nombre max d'enfants à charge", "nombrePersonneMaxACharge": "Nombre max de personnes à charge", "champsAncienete": "Champ de calcul de l'ancienneté", "epousePriseEnchargeParEpoux": "Nombre max d'épouses à charge par époux", "pensionVieillesse": "Pension vieillesse", "pourcentageAbatementIR": "Pourcentage d'abattement IR", "valPoint": "Valeur d'un point", "paieParPoints": "Calcul de la paie par points", "epouxPriseEnchargeParEpouse": "Nombre max d'époux à charge par épouse", "EChampsAnciennete.DateEmbauche": "Date d'embauche", "EChampsAnciennete.DateConfirmation": "Date de confirmation", "Formule": "Formule", "EIRPPFormular.CM_IRPP": "IRPP <PERSON>", "EIRPPFormular.CD_IRPP": "IRPP Côte d'Ivoire", "EIRPPFormular.CI_IRPP": "IRPP RDC", "ParametrePaie": "Paramètres de la paie", "PeriodePaie": "Période de la paie", "PayFunctions": "Fonctions de paie", "Calcul Paie Employe": "Calcul de la paie", "Analyse paie": "<PERSON><PERSON><PERSON> de <PERSON>e", "Paies_sur_exercice": "Paies sur l'exercice en cours", "Motifs de separation": "Motifs de séparation", "Motif de separation": "<PERSON><PERSON><PERSON> de <PERSON>", "ESenseRubriqueSeparation.Droit": "<PERSON><PERSON>", "ESenseRubriqueSeparation.Obligation": "Obligation", "EPeriodeCalculePayFonction.PeriodeCourante": "Période actuelle", "EPeriodeCalculePayFonction.PeriodeCouranteEtPrecedente": "Période actuelle et précédente", "EPeriodeCalculePayFonction.PeriodeCouranteEtNPrecedente": "Période actuelle et N précédentes", "EPeriodeCalculePayFonction.PeriodePrecedente": "Période précédente", "EPeriodeCalculePayFonction.NPeriodePrecedente": "N périodes précédentes", "EPeriodeCalculePayFonction.ToutesPeriodesPrecedentes": "Toutes les périodes précédentes", "EPeriodeCalculePayFonction.Toute": "Toutes les périodes", "EPeriodeCalculePayFonction.Annee": "Basé sur l'année de paie", "Periode_calcule": "Période de calcul", "Nbr_periode_precedetes": "Nombre de périodes précédentes", "Sense_separation": "Sens de séparation", "Arrondi_de_la_valeur": "Arrondi du résultat", "Type_arrondi": "Type d'arrondi", "Valeur_arrondi": "Base d'arrondi", "hint_Arrondi_de_la_valeur": "<PERSON><PERSON><PERSON><PERSON> le résultat du calcul", "hint_Type_arrondi": "Définition du type d'arrondi", "hint_Valeur_arrondi": "Base de l'arrondi", "ETypeArrondiPayFonction.ROUND_UP": "Par excès", "ETypeArrondiPayFonction.ROUND_DOWN": "<PERSON><PERSON> <PERSON><PERSON>", "ETypeArrondiPayFonction.NATURAL": "Naturel", "ETypeArrondiPayFonction.NONE": "Aucun", "hint_Calcule_sur_periodes_et_annee_precedentes": "Définition des périodes de paie/années sur lesquelles les valeurs du champ Base seront récupérées", "Calcule_sur_periodes_et_annee_precedentes": "Définition du calcul basé sur les paies précédentes", "hint_Periode_calcule": "Période", "hint_Nbr_periode_precedetes": "Nombre de périodes précédentes sur lesquelles les valeurs du champ Base seront récupérées", "hint_type_Calcule_Periode": "Type de calcul à appliquer sur les valeurs du champ Base", "hint_nombreMinAnneeService": "Âge minimum de la paie sur laquelle les valeurs du champ Base seront récupérées", "nombreMinAnneeService": "Nombre d'années minimal", "hint_nombreMaxAnneeService": "Âge maximum de la paie sur laquelle les valeurs du champ Base seront récupérées", "nombreMaxAnneeService": "Nombre d'années maximal", "type_Calcule_Periode": "Type de calcul", "ETypeCalculePeriodePayFonction.Average": "<PERSON><PERSON><PERSON>", "ETypeCalculePeriodePayFonction.Sum": "Somme", "ETypeCalculePeriodePayFonction.Min": "Minimum", "ETypeCalculePeriodePayFonction.Max": "Maximum", "hint_payfunction_Calculation": "Définissez la formule de la fonction de paie sur le calculateur", "payfunction_Calculation": "Formule de la fonction", "Valeur_constante": "<PERSON><PERSON> constante", "Default_Value": "Valeur par défaut", "conditional_statement": "Instruction conditionnelle", "valueIf": "Valeur si vraie", "valueElse": "<PERSON>ur si fausse", "Else If condition": "Condition sinon si", "Type_de_calcule": "Type de calcul", "Liste_des_valeurs_par_grade": "Liste des valeurs par grade", "Liste_des_valeurs_par_fonction": "Liste des valeurs par fonction", "Liste_des_valeurs_par_classe": "Liste des valeurs par classe", "Liste_des_valeurs_par_region": "Liste des valeurs par région", "formule_et_variables": "Formule de calcul et variables (Nombre, Base et Taux)", "evaluerEnPaieNormale": "Évaluer en paie normale", "evaluerEnPaieConge": "Évaluer en paie congé", "affichCumul": "A<PERSON><PERSON><PERSON> le cumul annuel", "option_de_rubrique": "Options de la rubrique", "Arrondi_valeur_finale": "Arrondi de la valeur après calcul", "ETypeArrondiRubrique.ROUND_UP": "Par excès", "ETypeArrondiRubrique.ROUND_DOWN": "<PERSON><PERSON> <PERSON><PERSON>", "ETypeArrondiRubrique.NATURAL": "Naturel", "ETypeArrondiRubrique.NONE": "Aucun", "contrainte_evaluation_de_rubrique": "Définition des contraintes d'évaluation de la rubrique", "hint_formule_et_variables": "Les valeurs des variables seront affichées sur le bulletin de paie même si elles ne font pas partie de la formule", "hint_f_variable_nombre": "La variable Nombre : sa valeur sera affichée sur le bulletin de paie même si elle ne fait pas partie de la formule", "hint_f_variable_base": "La variable Base : sa valeur sera affichée sur le bulletin de paie même si elle ne fait pas partie de la formule", "hint_f_variable_taux": "La variable Taux (valeur divisée par 100) : sa valeur sera affichée sur le bulletin de paie même si elle ne fait pas partie de la formule", "donnees_de_base": "Données de base", "evaluer_par_Type_de_paie": "Évaluer par type de paie", "Liste des PayFunction": "Liste des fonctions de paie", "EStatutConvocation.EnCours": "En cours", "dateCreate": "Date de création", "dateSuspendu": "Date de suspension", "dateCloture": "Date de clôture", "dateAnnulation": "Date d'annulation", "validerFinRelation": "Valider", "calculerFinRelation": "Calculer", "activerFinRelation": "Activer", "suspendFinRelation": "<PERSON><PERSON><PERSON><PERSON>", "cancleFinRelation": "<PERSON><PERSON><PERSON><PERSON>", "EEtatFinRelation.Encours": "Traitement en cours", "EEtatFinRelation.Suspendu": "Suspendu", "EEtatFinRelation.Annule": "<PERSON><PERSON><PERSON>", "EEtatFinRelation.Cloture": "Clôturé", "FinRelation": "Fin de relation", "MotifSeparation": "<PERSON><PERSON><PERSON> de <PERSON>", "Motif Separation": "<PERSON><PERSON><PERSON> de <PERSON>", "Fin relation": "Fin de relation", "Liste des FinRelation": "Fins de relation", "FinRelations": "Fins de relation", "CreateFinRelation": "Fin de relation - création", "hint_calcule_paie_fractionne": "Le système permet de fractionner la paie en deux en cas d'affectations pendant la période de paie.", "calcule_paie_fractionne": "Condition d'application en cas de fractionnement de paie.", "hint_cp_fractionNonAutorise": "Appliquer cette rubrique sur une seule fraction en cas de fractionnement de paie d'un employé.", "fractionNonAutorise": "Fractionnement non autorisé", "hint_cp_numeroFraction": "Numéro de la fraction sur laquelle la rubrique s'appliquera", "numeroFraction": "Numéro de fraction", "ParametresPaie": "Paramètres de la paie", "hint_nombreJourTravailMensuel": "Nombre de jours de travail dans une période de paie.", "hint_nombreEnfantMaxACharge": "Nombre maximal d'enfants à la charge de l’employé pour le calcul de la paie.", "hint_nombrePersonneMaxACharge": "Nombre maximal de personnes (conjoints et enfants) à charge pour le calcul de la paie.", "hint_champsAncienete": "Le champ dans la fiche employé utilisé pour calculer l'ancienneté.", "hint_epousePriseEnchargeParEpoux": "Plafonner le nombre d'épouses à la charge de l'époux", "hint_epouxPriseEnchargeParEpouse": "Plafonner le nombre d'époux à la charge de l'épouse", "hint_paieParPoints": "Calculer le salaire d'un employé en fonction du nombre de ses points.", "hint_valPoint": "Valeur d'un point lors du calcul du salaire par point.", "fractionnementPaie": "Fractionner la paie en cas d'affectation", "hint_fractionnementPaie": "Activez le fractionnement de la paie lorsqu'un employé a été affecté dans la période de paie. La paie sera fractionnée en deux, la première fraction se terminera à la date maximale des affectations.", "Filtres sur les employe": "Filtres sur les employés", "Calcule encours": "Calcul en cours", "PS_CAL_CLEAR_TITLE": "Supprimer les calculs ?", "PS_CAL_CLEAR_CONTENT": "Confirmer la suppression des calculs", "PS_CAL_CLOSE_TITLE": "<PERSON><PERSON><PERSON><PERSON><PERSON> la paie ?", "PS_CAL_CLOSE_CONTENT": "Les bulletins seront générés", "PS_CAL_CACLCULATE_TITLE": "Nouveau calcul ?", "PS_CAL_CACLCULATE_CONTENT": "Les anciens calculs seront supprimés", "PS_CAL_CACLCULATE_WITHFILTERS_TITLE": "Nouveau calcul avec filtre", "PS_CAL_CACLCULATE_WITHFILTERS_CONTENT": "Les anciens calculs seront supprimés. Seuls les employés inclus dans le filtre seront considérés.", "No.Employee.Found": "Aucun <PERSON>é retrouvé", "Arrondi_de_la_valeur_et_plafond": "Plafonnement et arrondi", "payfunction_plancher": "<PERSON><PERSON>", "payfunction_plafond": "Plafond", "imposable_et_cotisable": "Imposable et cotisable", "imposable_cotisable_salariale": "Part salariale", "imposable_cotisable_Patronale": "Part patronale", "tauxCotisable": "Taux cotisable", "plancherCotisable": "Plancher cotisable", "plafondCotisable": "Plafond cotisable", "plancherImposable": "Plancher imposable", "plafondImposable": "Plafond imposable", "tauxCotisablePatronale": "Taux cotisable patronal", "plancherCotisablePatronale": "Plancher cotisable patronal", "plafondCotisablePatronale": "Plafond cotisable patronal", "tauxImposablePatronale": "Taux imposable patronal", "plancherImposablePatronale": "Plancher imposable patronal", "plafondImposablePatronale": "Plafond imposable patronal", "EFormuleRubrique.NOMBRE": "Nombre", "calcule_part_salariale": "Calcul salarial", "calcule_part_patronale": "Cal<PERSON>l patronal", "EEvalFuctionType.SumMontantCotisable": "Somme des salaires cotisables", "EEvalFuctionType.SumMontantCotisablePatronale": "Somme des salaires cotisables patronaux", "EEvalFuctionType.SumMontantImposablePatronale": "Somme des salaires imposables patronaux", "EEvalFuctionType.ValeurSalaireBrut": "Valeur du salaire brut", "EModeRemboursementPret.Echeance": "Re<PERSON>urser une échéance", "EModeRemboursementPret.EcheancesRestantes": "Rembourser les échéances restantes", "hint_option_remboursement_pret": "Paramétrage lié à une rubrique dont la fonction de paie est de type échéance. Définit les échéances d'un prêt pour remboursement.", "option_remboursement_pret": "Remboursement des prêts", "estRemboursement": "Est un remboursement de prêt", "hint_cp_estRemboursement": "Si c'est un remboursement, cette rubrique doit être associée à une fonction de paie de type échéance prêt", "hint_cp_modeRemboursement": "Mode de remboursement", "modeRemboursement": "Mode de remboursement", "EEvalFuctionType.EcheancePret": "Échéance prêt", "valeur_exception_employe": "Attributions exceptionnelles par employé", "hint_valeur_exception_employe": "Les valeurs suivantes seront utilisées exceptionnellement pour chaque employé. Le champ valeur sera considéré comme zéro s'il est vide.", "hint_valeur_exception": "Ce champ valeur sera évalué à zéro s'il est vide.", "Formule.Null": "La formule est vide", "Base.Null": "La base est vide", "Nombre.Null": "Le nombre est vide", "Taux.Null": "Le taux est vide", "Employe.isRadie": "Emp<PERSON>é radié", "Motif.Null": "<PERSON><PERSON><PERSON> vide", "Employe.Null": "Employé vide", "FinRelation.Open.Record.Exist": "Un dossier en cours de traitement existe pour cet employé.", "montantCotisable": "Montant cotisable", "_tauxImposablePatronale": "Taux imposable patronal", "_montantImposablePatronale": "Montant imposable patronal", "_tauxCotisablePatronale": "Taux cotisable patronal", "_montantCotisablePatronale": "Montant cotisable patronal", "EComparisonType.IfElse": "Condition Si et Sinon", "EComparisonType.MaritalStatus": "Condition basée sur le statut matrimonial", "EComparisonType.TypeContrat": "Condition basée sur le type de contrat", "comparisonType": "Type de condition", "Married": "<PERSON><PERSON>", "Divorced": "<PERSON><PERSON><PERSON>", "Single": "Célibataire", "Widowed": "Veuf/Veuve", "hint_MaritalStatusCondition": "Définir une valeur pour chaque statut matrimonial. Si un champ est vide, sa valeur sera zéro", "maritalStatusCondition": "Valeur par statut matrimonial", "hint_condition_ms_married": "Valeur si l'employé est marié. Vide = zéro.", "hint_condition_ms_divorced": "Valeur si l'employé est divorcé. Vide = zéro.", "hint_condition_ms_single": "Valeur si l'employé est célibataire. Vide = zéro.", "hint_condition_ms_widowed": "Valeur si l'employé est veuf. Vide = zéro.", "hint_conversionRate": "Taux de change relatif à la devise par défaut. Le taux de change de la devise par défaut est 1.", "conversionRate": "Taux de <PERSON>", "Matricule.Null": "Le matricule est vide", "primaryCurrency": "<PERSON><PERSON> r<PERSON>", "splitNetSalaryPerCurrency": "Fractionner le salaire net par devise", "secondaryCurrency": "<PERSON><PERSON> secondaire", "primaryRate": "% devise de référence", "Liste des dataUpload": "Liste des chargements de données", "ID": "ID", "uploadDate": "Date de chargement", "uploadedFileName": "Nom du fichier", "successCount": "Su<PERSON>ès", "DataUploads": "Chargements de données", "UploadDate": "Date de chargement", "Fichier": "<PERSON><PERSON><PERSON>", "lineNumber": "Numéro de ligne", "Data Upload": "Chargement de données", "EDataUploadType.Country": "Pays", "EDataUploadType.IDDocumentType": "Types de documents d'identification", "EDataUploadType.PaymentMethod": "Mode de paiement", "EDataUploadType.Employee": "Employé", "EDataUploadType.EmployeeClass": "Classe", "EDataUploadType.EmployeeDutyPost": "Fonction", "EDataUploadType.EmployeeLevel": "Grade", "EDataUploadType.FinancialYear": "Exercice", "EDataUploadType.NetworkAndOrganizationChartTypes": "Type de réseau ou type d'organigramme", "EDataUploadType.NetworkAndOrganizationChart": "Rés<PERSON> ou organigramme", "EDataUploadType.PayFunctionConstant": "Fonction de paie -> <PERSON><PERSON><PERSON>", "EDataUploadType.PayFunction_ValuePerEmployee": "Fonction de paie -> Valeur par employé, grade, fonction, classe, nœud", "EDataUploadType.PayslipItem": "Éléments de la paie", "EDataUploadType.AgenceLiaison": "Comptes de liaison des agences", "EDataUploadType.AgenceRubrique": "Comptes de rubrique des agences", "EDataUploadType.Sanction": "Sanctions", "EDataUploadType.TypeSanction": "Type de sanctions", "EDataUploadType.EmployeManager": "Manager des employés", "EDataUploadType.CumulValeur": "Cumul", "EDataUploadType.AgenceEmploye": "Agence des employés", "EDataUploadType.TypeConge": "Type de congés (absences)", "EDataUploadType.TypePret": "Type de prêt (crédits)", "EDataUploadType.Pret": "<PERSON><PERSON><PERSON><PERSON>", "EDataUploadType.Rubrique": "Rubriques", "EDataUploadType.Conge": "<PERSON><PERSON><PERSON>", "EDataUploadType.ProprieteEvaluation": "Éléments de contrôle - module Évaluation", "EDataUploadType.PeriodiciteEvaluation": "Périodicité - module Évaluation", "EDataUploadType.TypeJustificatifEvaluation": "Type justificatifs - module Évaluation", "EDataUploadType.ElementControleTypeJustificatif": "Éléments de contrôle des types justificatif", "EDataUploadType.NatureJustificatifEvaluation": "Nature justificatifs - module Évaluation", "EDataUploadType.CritereEvaluation": "Critères - module Évaluation", "EDataUploadType.RemboursementPret": "Remboursements de prêt", "EDataUploadType.Chapitre": "Chapitre", "EDataUploadType.CompteEmploye": "Compte des employés", "EDataUploadStatus.Created": "<PERSON><PERSON><PERSON>", "EDataUploadStatus.UploadFailed": "Échec de téléchargement", "EDataUploadStatus.Processing": "Traitement", "EDataUploadStatus.Succeeded": "Su<PERSON>ès", "EDataUploadStatus.Failed": "Échec", "EDataUploadItemStatus.Success": "Su<PERSON>ès", "EDataUploadItemStatus.Failed": "Échec", "Lignes du chargement": "Lignes du chargement", "Information de base": "Info de base", "numberOfItems": "Nombre d'éléments", "DataUpload": "Chargement de données", "UploadType.Not.Imlemented": "Type de chargement non implémenté", "Item.Failed": "Échec d'un élément", "Exception.On.Item": "Exception sur une ligne", "Invalid.Alphanumeric.Code": "Caractère invalide dans le code", "One.Item.Or.More.Failed": "Un ou plusieurs éléments ont échoué", "Exception.On.Or.More.Items": "Une exception s'est produite sur un ou plusieurs éléments", "TypeNoeud.Not.Found": "Type de nœud introuvable", "xlsx.Extension.Required": "Le type de fichier requis est xlsx", "File.Null": "Le fichier est vide", "Description.Null": "La description est vide", "Type.Null": "Type vide", "Upload.Contains.Empty Rows": "Ligne(s) vide(s) présente(s)", "CreatePayFunction": "Création d'une fonction de paie", "Liste_des_valeurs_par_statut_employe": "Liste des valeurs par statut de l'employé", "typeContratCondition": "Valeurs par type de contrat", "valeurCDD": "Contrat à durée déterminée", "valeurCDI": "Contrat à durée indéterminée", "hint_typeContratCondition": "Valeur par type de contrat. Si une valeur est vide, elle sera considérée comme zéro", "StatutContrat": "Statut du contrat", "StatutDeEmploye": "Statut de l'employé", "StatutsEmploye": "Statuts des employés", "Liste des StatutsEmploye": "Liste des statuts des employés", "StatutEmploye": "Statut d'employé", "PayFunction.Base.Field.ReturnType.Must.Be.Number": "Le type de retour doit être un nombre", "ValeurArrondi.Null": "La valeur d'arrondi est vide", "ValeurArrondi.Must.Be.Greater.Than.0": "La valeur d'arrondi doit être supérieure à zéro", "Value.Null": "La valeur est vide", "Error.Parsing.Constant.Value": "Erreur de conversion de la constante", "Rubrique.Null": "Rubrique vide", "PeriodeCalcule.NullFor.PeriodBased.Type": "La période de calcul est nulle", "NombrePeriodePrecedente.Must.Be.GT.0": "Le nombre de périodes précédentes doit être supérieur à zéro", "TypeCalculePeriode.Is.Required": "Le type de calcul est requis", "PayFonction.NombreMinAnneeService.Is.Null": "Le nombre minimal d'années est nul", "PayFonction.NombreMinAnneeService.Must.Be.Between.0.150": "Le nombre minimal d'années doit être compris entre 0 et 150", "PayFonction.NombreMaxAnneeService.Is.Null": "Le nombre maximal d'années est nul", "PayFonction.NombreMaxAnneeService.Must.Be.Between.0.150": "Le nombre maximal d'années doit être compris entre 0 et 150", "TypeCalculePeriode.NombreMaxAnneeService.Must.Be.GT.NombreMinAnneeService": "Le nombre maximal d'années doit être supérieur au nombre minimal", "PayFonction.Base.Field.Required": "Le champ de base est obligatoire", "Calculation.Empty": "Le calcul est vide", "Range.Operation.Type.Null": "Le type d'opération pour la plage est vide", "ComparisonType.Null": "Le type de comparaison est nul", "Groupe.Null": "Le groupe est nul", "Nature.Null": "La nature est nulle", "ModeRemboursement.Pret.Null": "Le mode de remboursement du prêt est nul", "NumeroFraction.Null": "Le nombre de fractions est nul", "NumeroFraction.Not.Between.1.2": "Le nombre de fractions doit être compris entre 1 et 2", "SenseSeparation.Null": "Le sens de séparation est nul", "Rubrique.Not.Found": "R<PERSON><PERSON>que introuvable", "typeDePret": "Type de prêt", "hint_typeDePret": "Si vide, tous les types de prêt seront considérés", "Some.Employees.Are.Not.Evaluated": "La paie des employés suivants n'a pas été calculée", "Details des rubriques": "Détails des rubriques", "nombreJourOuvree": "Jours ouvrés", "ESexe.MASCULIN": "MASCULIN", "ESexe.FEMININ": "FÉMININ", "CalculPaieEmploye": "Calcul de la paie", "Exercice.Date.Dabut.Null": "Date de début vide", "Exercice.Date.Fin.Null": "Date de fin vide", "Nombre_de_critere_realise / Norme": "Nombre de critères réalisés / Norme", "Nombre_de_critere_realise / Objectif": "Nombre de critères réalisés / Objectif", "Taux/norme": "Taux/norme", "Taux/Objectif": "Taux/objectif", "EMPLOYES_FIN_RELATION": "Employés en fin de relation", "EDataUploadType.PayslipHistory": "Historique paie", "EDataUploadType.AffectationHistory": "Historique des affectations", "Code.Element.Affectation.Null": "Code de l'élément d'affectation vide", "TypeAffectation.Null": "Type d'affectation vide", "numRegister": "Numéro de registre", "numRegister1": "Numéro de registre 1", "numRegister2": "Numéro de registre 2", "Nombre_absence": "Nombre d'absences", "Nombre_employe": "Nombre d'employés", "Congé annuel": "Congé annuel", "DeleteLastPayrollTitle": "Supprimer la dernière paie clôturée", "Cannot.Delete.Multiple.Payroll": "Impossible de supprimer plusieurs paies", "Last.Payroll.Deleted": "Dernière paie supprimée", "Preceeding.Period.Not.Found": "Période de paie précédente non trouvée", "Preceeding.Period.Id.Null": "ID de la période de paie précédente vide", "Current.Period.Not.Found": "Période de paie actuelle non trouvée", "Payroll.Delete.Disabled": "Suppression de la paie désactivée", "DELETE_LAST_PAYROLL_TITLE": "Suppression de la dernière paie clôturée", "DELETE_LAST_PAYROLL_CONTENT": "Confirmez-vous la suppression de la dernière paie clôturée ?", "CalcCompleted": "<PERSON><PERSON><PERSON><PERSON>", "Calculation.Initiated": "Calcul initié", "Calculation.Stopped": "<PERSON><PERSON><PERSON>", "Calculation.Already.Cancelled": "Cal<PERSON>l dé<PERSON><PERSON> annu<PERSON>", "Calculation.Already.Done": "Cal<PERSON>l d<PERSON> effectué", "Calculation.Executor.Null": "Calculateur nul", "calculationOngoing": "Calcul en cours", "PeriodePaie.Encours.Not.Found": "Période de paie en cours non trouvée", "EDataUploadType.StatutEmploye": "Statut de l'employé", "EDataUploadType.Entreprise": "Détails de l'entreprise", "EDataUploadType.PayFunctionValeurParEmploye": "Fonction de paie -> Valeurs constantes par <PERSON>é", "EDataUploadType.PayFunctionCalculation": "Fonction de paie -> <PERSON><PERSON>l", "EDataUploadType.PayFunctionValeurParClasse": "Fonction de paie -> Valeur par classe", "EDataUploadType.PayFunctionValeurParFonction": "Fonction de paie -> Valeur par fonction", "EDataUploadType.PayFunctionValeurParGrade": "Fonction de paie -> Valeur par grade", "parametre_paie_options_cumul": "Option d'affichage des cumuls", "hint_afficherCumulMensuel": "Ajouter les cumuls mensuels des rubriques aux cumuls du bulletin", "afficherCumulMensuel": "Afficher cumul mensuel", "hint_afficherCumulSalBrutImposable": "Ajouter le salaire brut imposable aux cumuls", "afficherCumulSalBrutImposable": "Afficher salaire brut imposable", "hint_afficherCumulSalBrutCotisable": "A<PERSON>ter le salaire brut cotisable aux cumuls", "afficherCumulSalBrutCotisable": "Afficher salaire brut cotisable", "hint_afficherCumulSalBrut": "A<PERSON>ter le salaire brut aux cumuls", "afficherCumulSalBrut": "Afficher salaire brut", "hint_afficherCumulChargesPatronal": "Ajouter les charges patronales aux cumuls", "afficherCumulChargesPatronal": "Afficher charges patronales", "Numero_acte_naissance": "Numéro de l'acte de naissance", "Lieu de naissance": "Lieu de naissance", "Date_de_la_Signature": "Date de signature", "Date_effet": "Date d'effet", "Type_Affectation": "Type d'affectation", "Ref._de_note": "Réf. de note", "Casier_Judiciaire": "<PERSON><PERSON><PERSON> judicia<PERSON>", "Lieu_d_établissement": "Lieu d'établissement", "Nom_du_magistrat": "Nom du magistrat", "TPI_qui_a_établi": "TPI qui a établi", "Numero_contribuable": "Numéro du contribuable", "Nom_de_la_Mere": "Nom de la mère", "Age_Mediane": "<PERSON><PERSON> m<PERSON>", "Gestion de prets": "Gestion de prêts", "Type pret": "Type de prêt", "Listes des prets": "Liste des prêts", "Liste_des_types_de_pret": "Liste des types de prêt", "Next": "Suivant", "Last": "<PERSON><PERSON>", "Fixer_un_max_sur_salaire": "Fixer un max sur le salaire", "Liste_demandes_Pret": "Liste des demandes de prêt", "Employé": "Employé", "Etat Pret": "État du prêt", "previous": "Précédent", "First": "Premier", "Code Pret": "Code de prêt", "Date validation": "Date de validation", "Importe": "Importé", "Montant restant": "<PERSON><PERSON> restant", "Medecins": "M<PERSON><PERSON><PERSON><PERSON>", "Hopitaux": "<PERSON><PERSON><PERSON><PERSON>", "Laboratoire d'analyse": "Laboratoire d'analyse", "Type de visite": "Type de visite", "Examen medicaux": "Examens médicaux", "Specialisation": "Spécialisation", "Convocation medicale": "Convocation médicale", "Liste des médécins": "Liste des médecins", "Medecine de travail": "Médecine du travail", "Hopital de fonction": "Hôpital de fonction", "Telephone": "Téléphone", "Spécialisation médicale": "Spécialisation médicale", "Liste des hopitaux": "Liste des hôpitaux", "Localisation": "Localisation", "Libellé": "Libellé", "Région": "Région", "Date de réception": "Date de réception", "Convocations médicales": "Convocations médicales", "Date envoie convocation": "Date d’envoi convocation", "Périodique": "Périodique", "Génération automatique": "Génération automatique", "Prix appr.": "Prix approuvé", "Descripcion": "Description", "Médécins agréés": "Médecins agréés", "Laboratoires d' analyse agréés": "Laboratoires d’analyse agréés", "Gestion des formations": "Gestion des formations", "Niveau formation": "Niveau de formation", "Rubrique  d'évaluation financiere": "Rubrique d’évaluation financière", "Type Formation": "Type de formation", "Indicateur évaluation de formation": "Indicateur d’évaluation de formation", "Critere notation": "Critère de notation", "Offre formation": "Offre de formation", "Type Entreprise": "Type d’entreprise", "FinancialEvaluationSection - Ajout": "FinancialEvaluationSection - Ajout", "TypeFormation - Ajout": "Type de formation - Ajout", "RatingIndicator - Ajout": "Indicateur de notation - <PERSON><PERSON><PERSON>", "ScoringCriteria - Ajout": "Critère de notation - A<PERSON>t", "TrainingOffer - Ajout": "Offre de formation - Ajout", "Entreprise formatrice": "Entreprise formatrice", "Niveau de formation": "Niveau de formation", "Indicateur notation offre": "Indicateur de notation de l’offre", "Critere notations offre": "Critère de notation de l’offre", "Evaluation financière": "Évaluation financière", "Liste_indicators": "Liste des indicateurs", "Objectifs / Indicateurs d'evaluation": "Objectifs/Indicateurs d’évaluation", "Level Formation - Ajout": "Niveau de formation - Ajout", "Panel de control": "Panneau de contrôle", "Mes Operations": "Mes opérations", "Recrutemnet": "Recrutement", "Gestion des Absences": "Gestion des absences", "Gestion des Sanctions": "Gestion de la discipline", "Gestion des Missions": "Gestion des missions", "Entreprise": "Entreprise", "Parametres": "Paramètres", "Exercices": "Exercices", "type_de_piece": "Type de pièce", "Structures": "Structures", "Message": "Message", "Connexion BD": "Connexion BD", "host": "Host", "Extraction base de donnee": "Extraction base de données", "port": "Port", "Choose table": "Choisir la table", "infoConnect": "Info connect", "DIRECTION GENERALE": "DIRECTION GÉNÉRALE", "ML_Type_de_piece - Ajout": "ML_Type_de_piece - <PERSON><PERSON>t", "Code type de noeud localisation ville": "Code type de nœud localisation ville", "Serveur SMS": "Serveur SMS", "Serveur EMAIL": "Serveur e-mail", "Serveur": "Ser<PERSON><PERSON>", "Mot de passe": "Mot de passe", "Nombres de jours avant alerte": "Nombre de jours avant alerte", "Paramètres Alerte Contrat": "Paramètres alerte contrat", "Objet Email": "Objet e-mail", "Modèle SMS": "<PERSON><PERSON><PERSON><PERSON>", "Modèle Email": "Modèle e-mail", "Manager a contacter": "Manager à contacter", "Choisir un Manager": "Choisir un manager", "Notification SMS Absence": "Notification SMS absence", "Nombre de Jours ouvrables sur l'exercice": "Nombre de jours ouvrables sur l’exercice", "Date Butoire": "Date butoir", "Date butoire validation manager*": "Date butoir validation manager*", "Param tre Tableau de Bord": "Paramètre tableau de bord", "Couleur Diagramme": "Couleur diagramme", "Masculin": "<PERSON><PERSON><PERSON><PERSON>", "Feminin": "Fé<PERSON>n", "Nombre (employ s, cong s)": "Nombre (employés, congés)", "Age et Ann es experience": "Âge et années d'expérience", "Entreprise - CurrentMode2": "Entreprise - CurrentMode2", "numRegistre": "Numéro de registre", "Sigle": "<PERSON><PERSON>", "ville": "Ville", "boitePostal": "Boîte postale", "Profile": "Profil", "Mon profile": "Mon profil", "Contrat": "Contrat", "Curriculum Vitae": "Curriculum Vitae", "Historiques": "Historiques", "Formation": "Formation", "Ecole": "École", "Aptitudes Professionnelles": "Aptitudes professionnelles", "Langues": "<PERSON><PERSON>", "Mension Ecrire": "Mention É<PERSON>rire", "Mension Parle": "Mention Parler", "Retirer": "<PERSON><PERSON><PERSON>", "Date effet": "Date d'effet", "Date decret": "Date du décret", "Type Affectation": "Type d'affectation", "Affectation": "Affectation", "Contrats": "Contrats", "Realisations": "Réalisations", "Rapports Realisations": "Rapports des réalisations", "Demande Conge": "De<PERSON><PERSON> de congés/absences", "Note disciplinaire": "Note disciplinaire", "Note Disciplinaire": "Note disciplinaire", "Demande Mission": "<PERSON><PERSON><PERSON> de <PERSON>", "Retour Mission": "Retour de mission", "Demande Pret": "<PERSON><PERSON><PERSON>", "Bulletins": "Bulletins", "Convocation médicale": "Convocation médicale", " Liste-De-Mes-Realisations": "Liste de mes réalisations", "Periode": "Période", "Export document": "Exporter document", "Fiche": "Fiche", "Employe de type": "Employé de type", "Type contrat": "Type de contrat", "Date debut": "Date de début", "SYNTHESE": "SYNTHÈSE", "Demande_Conge - Ajout": "<PERSON><PERSON><PERSON> <PERSON><PERSON>", "Mensualité": "Mensualité", "Mes Bulletins": "Mes bulletins", "Liste des convocations médicales": "Liste des convocations médicales", "Liste-Des-Realisations": "Liste des réalisations", "GenerationdipeMagnetic": "Dipes magnétiques", "Demandes de derogation": "De<PERSON><PERSON> de d<PERSON>", "List of profiles": "Liste des profils", "List of user level": "Liste des niveaux", "List of users": "Liste des utilisateurs", "Location": "Localisation", "PeriodePaie - CurrentMode2": "PériodePaie - CurrentMode2", "Liste des groupes de rubrique": "Liste des groupes de rubriques", "Groupe rubrique": "Groupe rubrique", "GroupeRubrique - Ajout": "GroupeRubrique - Ajout", "libeleEntete": "Libellé en en-tête", "Liste_étapes_recrutement": "Liste des étapes de recrutement", "Etape du recrutement": "Étape du recrutement", "critere_recrutement": "Critère de recrutement", "Liste_fonction": "Liste des fonctions", "Pourcentage_evaluation": "Pourcentage d'évaluation", "GenerationDuRecapEvaluation": "Génération du récapitulatif d'évaluation", "Date_debut": "Date de début", "Date_fin": "Date de fin", "GenerationRecaputilatifEvaluation": "Génération du récapitulatif d'évaluation", "Generer_recap": "Générer le récapitulatif d'évaluation", "GenerationRecapEvaluation": "Récapitulatif d'évaluation", "Type_ressource": "Type de ressource", "TypeRessource": "Type de ressource", "Anciennete": "Ancienneté", "Etat Demande": "État de la demande", "DateDemande": "Date de la demande", "DateFin": "Date de fin", "Type_Ressource": "Type de ressource", "demande_recrutement": "<PERSON><PERSON><PERSON> de recrutement", "Liste_niveaus": "Liste des niveaux", "Candidats_Externe": "Candidats externes", "Generer_bilan_salariale": "<PERSON><PERSON><PERSON><PERSON> bilan salarial", "Libelle_pret": "Libellé <PERSON>", "Etat_pret": "État du prêt", "Liste_action_pret": "Liste des actions sur les prêts", "Employe_Responsable": "Employé responsable", "Code_Pret": "Code prêt", "Action_pret": "Action sur le prêt", "Suspendre_pret": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Proprietaire_pret": "Proprié<PERSON> prêt", "Date_pret": "Date du prêt", "EnCours": "En cours", "Rembourse": "Re<PERSON><PERSON><PERSON>", "Motif_Suspension_Pret": "<PERSON><PERSON><PERSON> de <PERSON> du <PERSON>r<PERSON>", "Motif_Reactivation_Pret": "Motif de réactivation du prêt", "Nombre_echeance_rembourse": "Nombre d'échéances remboursées", "Nombre_echeance_total": "Nombre total d'échéances", "Motif_reprise": "<PERSON><PERSON><PERSON> reprise", "Motif_suspension": "<PERSON><PERSON><PERSON>", "Description_de_la_formation": "Description de la formation", "Objectifs_de_la_formation": "Objectifs de la formation", "Objectifs_et_Description": "Objectifs et description", "Durée_de_la_formation_en_jours": "Durée de la formation en jours", "Détails_de_la_formation": "Détails de la formation", "Liste_employe_ayant_de_besoin_de_formation": "Liste des employés ayant besoin de formation", "Cout_par_participants": "Coût par participant", "Session_de_formation_disponible": "Session de formation disponible", "Details_de_formation": "Détails de la formation", "Manager(s)": "Manager(s)", "Date_validation": "Date de validation", "Campagne_recrutement": "Campagne de recrutement", "Code_campagne": "Code de la campagne", "EtapeRecruParent": "Étape de recrutement parent", "Etape_Parent": "Étape parent", "planning_recrutement": "Planning de recrutement", "Planning_campagne_recrutement": "Planning des campagnes de recrutement", "Coisir_Etape": "Choisir une étape", "pourcentage_evaluation": "Pourcentage d'évaluation", "Choisir_candidat": "Choisir un candidat", "Choisir_employe": "Choisir un employé", "Besoin_recrutement": "<PERSON><PERSON>in de recrutement", "Intitule_poste": "Intitulé du poste", "Nombre_place": "Nombre de places", "Date_ouverture": "Date d'ouverture", "Planning_recrutement": "Planning de recrutement", "Planning_conge_valide": "Planning des congés validés", "Charger_cv": "Charger le CV", "EDataUploadType.CvCandidat": "CV du candidat", "Besoin_existe_deja": "Ce besoin existe déjà", "Une_campagne_encours_pour_besoin": "Une campagne est déjà en cours pour ce besoin", "Nombre_de_place": "Entrez le nombre de places pour ce besoin", "Fonction.Null": "Entrez la fonction à pourvoir", "Post.Null": "Aucun poste vacant", "Fonction.Code.Null": "Le code de la fonction n'existe pas", "Candidats.Null": "La liste des candidats est vide", "Sexe.Null": "Entrez le sexe du candidat", "Experience.Null": "Précisez le nombre d'années d'expérience du candidat", "NoteMin.Null": "Entrez la note minimale de l'étape", "EtapeRecrutement.Null": "Ajoutez l'étape de recrutement", "Parent.Exist": "Cette étape est un parent, elle ne peut donc pas avoir de parent", "NoteMax.Null": "Entrez la note maximale", "Besoin.OK": "Be<PERSON>in de recrutement généré", "Liste des affectation": "Liste des affectations", "EStatutDemandeFormation.EnAttente": "En attente", "EStatutDemandeFormation.Valide": "<PERSON><PERSON><PERSON>", "EStatutDemandeFormation.Rejette": "<PERSON><PERSON><PERSON>", "ETypeFormation.Externe": "Externe", "ETypeFormation.Interne": "Interne", "Formation.Not.Found": "Formation non trouvée", "SessionFormation.Not.Found": "Session de formation non trouvée", "Employe.Not.Found": "Employé non trouvé", "Employe.Deja.Present.SessionFormation": "Employé d<PERSON>jà présent dans la session de formation", "Employe(s).Deja.Presents": "Employé d<PERSON>jà présent dans la session de formation", "Manager.Employe.Empty": "Le manager de l'employé est vide", "DemandeFormation.Already.Exist": "La demande de formation existe déjà", "UniqueIdentificationNumber.Null": "Un utilisateur avec le même numéro d'identification unique existe déjà.", "Type_Contrat": "Type de contrat", "nombre_place": "Nombre de places", "EmployeNotFound": "Employé non trouvé", "Parametres serveur SMS": "Paramètres serveur SMS", "Parametres serveur Email": "Paramètres serveur e-mail", "Alerte Contrat echeance": "Alerte échéance de contrat", "Parametres notification alerte contrat echeance": "Paramètres notification alerte échéance de contrat", "Configuration parametre dipes magnetiques": "Configuration du paramètre dipes magnétiques", "Parametres_generaux": "Paramètres généraux", "Parametres_notifications": "Paramètres notifications", "configuration contribuable pour le dipe magnetique": "Configuration contribuable pour le dipe magnétique", "Liste_element_facturation": "Liste des éléments de facturation", "Jours_ouvrables": "Jours ouvrables", "Laboratoires d analyses": "Laboratoires d’analyses", "Motif_absence": "<PERSON><PERSON><PERSON>'absence", "Liste_employé_absent_session": "Liste des employés absents à la session de formation", "Planaing_recrutement": "Planning de recrutement", "Experience_pro": "Expérience professionnelle", "Formations": "Formations", "DATE_NAISSANCE": "Date de naissance", "Situation_matrimoniale": "Situation matrimoniale", "AnneeExperience": "Année d'expérience", "TRES_BIEN": "Très bien", "Etat_candidature": "État de la candidature", "NOM_PRENOM": "Nom & prénom", "Statu_matrimonail": "Statut matrimonial", "Liste_candidatures": "Liste des candidatures", "Intitule_campagne": "Intitulé de la campagne", "Intitule_Poste": "Intitulé du poste", "Intittule_poste": "Intitulé du poste", "Nom_candidat": "Nom du candidat", "Date_create": "Date de création", "Etape_recrutement": "Étape de recrutement", "Jour(s)": "Jour(s)", "MiseAPied": "Mise à pied", "true": "O<PERSON>", "false": "Non", "Ville.Null": "Veuillez entrer la ville", "Requête": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Agence.Null": "Veuillez choisir l'agence", "DateDebut.Null": "Veuillez entrer la date de début", "DateFin.Null": "Veuillez entrer la date de fin", "TypeContrat.Null": "Veuillez préciser le type de contrat", "Parent.SelfReference": "Une étape de recrutement ne peut pas être son propre parent.", "Element.IsFils": "L'étape de recrutement est déjà un sous-étape", "Parent.IsNotParent": "L'étape de recrutement choisie n'est pas un parent", "Consulter_offre_de_formation": "Consulter l'offre de formation", "Entreprise_formatrice": "Entreprise formatrice", "Type_Formation": "Type de formation", "Catégorie_Formation": "Catégorie de formation", "Niveau_Formation": "Niveau de formation", "Durée_en_jours": "Durée en jours", "Date_de_début": "Date de début", "Nombre_de_jours": "Nombre de jours", "Employe_Absent": "<PERSON><PERSON><PERSON><PERSON> absent", "Formations_requises_par_la_fonction": "Formations requises par la fonction", "Elément_de_facturation": "Élément de facturation", "Type_de_facturation": "Type de facturation", "Compte_à_créditer": "Compte à créditer", "Entrer_le_Montant": "<PERSON><PERSON><PERSON> le montant", "Critères_d_évaluation": "Critères d'évaluation", "Historique_de_paiement": "Historique de paiement", "Budgétisation": "Budgétisation", "Employés_Absents_à_la_session_de_formation": "Employés absents à la session de formation", "Participants_à_la_session_de_formation": "Participants à la session de formation", "Ajouter_les_employés_à_la_session_de_formation": "Ajouter les employés à la session de formation", "Détails_session_de_formation": "Détails de la session de formation", "Date_debut_formation": "Date de début de la formation", "Liste_des_éléments_de_facturation": "Liste des éléments de facturation", "Element_de_facturation": "Élément de facturation", "Nombre_de_participants": "Nombre de participants", "Nombre_de_participants_Réels": "Nombre de participants réels", "Liste_des_participants_à_la_session": "Liste des participants à la session", "Eléments_de_facturation": "Éléments de facturation", "Coût_total": "Coût total", "Montant_restant_à_verser": "Montant restant à verser", "Montant_Payé": "<PERSON><PERSON> payé", "Evaluations": "Évaluations", "Element_Facturation": "Élément de facturation", "Numéro_de_compte": "Numéro de compte", "Clé": "Clé", "Liste_des_offres_de_formation": "Liste des offres de formation", "Nombre_de_jours_de_la_formation": "Nombre de jours de formation", "Coût_par_participants": "Coût par participant", "Session_Formation": "Session de formation", "Cout_Par_Participant": "Coût par participant", "Cout_Globale": "Coût global", "Éditer": "É<PERSON>er", "Entreprise_Formatrice": "Entreprise formatrice", "Ajouter_Employe": "Ajouter des employés", "Ajouter_les_employés_à_la_session": "Ajouter les employés à la session", "Sauvegarder": "<PERSON><PERSON><PERSON><PERSON>", "Paiement": "Paiement", "Liste_des_types_de_formations": "Liste des types de formation", "Liste_des_formations": "Liste des formations", "Liste_des_sessions_de_formations": "Liste des sessions de formation", "Motif_refus_manager": "<PERSON><PERSON><PERSON> manager", "Motif_refus_rh": "<PERSON><PERSON><PERSON> de refus RH", "Document Conge": "De<PERSON><PERSON> de congés/absences", "Document Mission": "<PERSON><PERSON><PERSON> de <PERSON>", "Fautes": "<PERSON><PERSON><PERSON>", "Action_pret_details": "Action sur le prêt - Détails", "JUSTIFIER": "<PERSON><PERSON><PERSON><PERSON>", "ATTENTE_JUSTIFICATIF": "En attente de justification", "JUSTIFIER_AVEC_RETARD": "Justifié avec retard", "Delai de reponse à  la note disciplinaire expiré": "Le délai de réponse à la note disciplinaire a expiré !", "Veillez_saisir_une_reponse_valide": "Veuillez saisir une réponse valide !", "Repondre": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Annuler_note_disciplinaire": "Annuler note disciplinaire", "Motif_annulation": "<PERSON><PERSON><PERSON> d'annulation", "Note_disciplinaire": "Note disciplinaire", "ANNULER": "<PERSON><PERSON><PERSON>", "Nombre_employes_Departement": "Nombre d'employés par département", "EProfileType.Employe": "Employé", "EProfileType.Manager": "Manager", "Telecharger": "Télécharger", "Parametres_serveur_SMS": "Paramètres serveur SMS", "Parametres_notification_alerte_contrat_echeance": "Paramètres notification alerte échéance de contrat", "Alerte_Contrat_echeance": "Alerte échéance de contrat", "Parametres_serveur_Email": "Paramètres du serveur e-mail", "Age_Annees_experience_Couleur_diagramme": "<PERSON><PERSON>, ann<PERSON> d'expérience, couleur diagramme", "Telecharger_pdf": "Télécharger PDF", "RETOUR": "Retour", "EN_ATTENTE": "En attente", "REFUSER": "<PERSON><PERSON><PERSON><PERSON>", "Autres_éléments": "Autres éléments", "Choisir_le_manager": "<PERSON><PERSON> le manager", "OuvrirEntreprise": "<PERSON>uv<PERSON>r entreprise", "EnregistrerEntreprise": "Enregistrer l'entreprise", "OuvrirParametres": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "OuvrirExercices": "<PERSON><PERSON><PERSON><PERSON><PERSON> exercices", "AjouterExercices": "Ajouter exercice", "DemarrerExercices": "<PERSON><PERSON><PERSON><PERSON> exercice", "CloturerExercices": "<PERSON><PERSON><PERSON><PERSON><PERSON> exercice", "OuvrirDevises": "<PERSON><PERSON><PERSON><PERSON><PERSON> devises", "OuvrirFonction": "Ouvrir fonction", "AjouterFonctions": "Ajouter fonction", "OuvrirGrade": "Ouvrir grade", "AjouterGrade": "Ajouter grade", "OuvrirClasse": "Ouvrir classe", "AjouterClasse": "Ajouter classe", "OuvrirTypeDePieceId": "Ouvrir type de pièce ID", "AjouterTypeDePieceId": "Ajouter type de pièce ID", "OuvrirStatutEmploye": "<PERSON><PERSON><PERSON><PERSON><PERSON> statut <PERSON>", "AjouterStatutEmploye": "A<PERSON>ter statut employé", "OuvrirChangementDeDonnee": "Ouvrir chargement de données", "AjouterChangementDeDonnee": "Ajouter chargement de données", "OuvrirStructure": "Ouvrir structure", "OuvrirTypeDeNoeuds": "Ouvrir type de nœuds", "AjouterTypeDeNoeuds": "Ajouter type de nœuds", "OuvrirOrganigramme": "<PERSON><PERSON><PERSON><PERSON>r organigramme", "OuvrirReseau": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "OuvrirPosteVacant": "<PERSON><PERSON><PERSON><PERSON><PERSON> poste vacant", "OuvrirPeriodiciteEvaluation": "Ouvrir périodicité d'évaluation", "AjouterPeriodiciteEvaluation": "Ajouter périodicité d'évaluation", "OuvrirPropriete": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "AjouterPropriete": "<PERSON><PERSON><PERSON> propri<PERSON>", "OuvrirTypesJustificatif": "Ouvrir types de justificatifs", "AjouterTypesJustificatif": "Ajouter types de justificatif", "OuvrirTypesAutomatiques": "Ouvrir types automatiques", "AjouterTypesAutomatiques": "Ajouter types automatiques", "OuvrirNatureJustificatif": "Ouvrir nature du justificatif", "AjouterNatureJustificatif": "Ajouter nature du justificatif", "OuvrirCritere": "<PERSON><PERSON><PERSON><PERSON><PERSON> c<PERSON>", "AjouterCritere": "<PERSON><PERSON><PERSON> critère", "OuvrirCritereParFonction": "Ouvrir critère par fonction", "AjouterCritereParFonction": "Ajouter critère par fonction", "OuvrirLissageParAgence": "Ou<PERSON><PERSON>r lissage par agence", "AjouterLissageParAgence": "Ajouter lissage par agence", "OuvrirLissageParEmploye": "<PERSON><PERSON><PERSON><PERSON>r lissage par <PERSON>é", "AjouterLissageParEmploye": "Ajouter lissage par <PERSON>é", "OuvrirMonProfile": "<PERSON><PERSON><PERSON><PERSON>r mon profil", "ModifierProfile": "Modifier le profil", "OuvrirMesObjectifs": "<PERSON><PERSON><PERSON><PERSON><PERSON> mes objectifs", "EnregistrerObjectifs": "Enregistrer mes objectifs", "ImprimerObjectifs": "Imprimer objectifs", "EnvoyerObjectifsEnValidation": "Envoyer mes objectifs en validation", "AjouterPeriodeEvaluation": "Ajouter période d’évaluation", "OuvrirMesRealisations": "Ouv<PERSON>r mes réalisations", "EnvoyerRealisationEnValidation": "Envoyer mes réalisations en validation", "OuvrirMesRapportRealisations": "Ouvrir mes rapports de réalisations", "ImprimerRapportRealisation": "Imprimer mes rapports de réalisations", "OuvrirMaDemandeConge": "<PERSON><PERSON><PERSON><PERSON><PERSON> demande de congé", "AjouterDemandeConge": "A<PERSON>ter demande de congé", "OuvrirMaDemandeFormation": "Ouvrir demande de formation", "AjouterDemandeFormation": "Ajouter demande de formation", "OuvrirMesSessionsDeFormations": "Ouv<PERSON>r mes sessions de formation", "OuvrirMaListeDemandeFormation": "<PERSON><PERSON><PERSON><PERSON>r ma liste de demandes de formation", "OuvrirMesNotesDisciplinaires": "Ouvrir mes notes disciplinaires", "OuvrirMesSanctions": "<PERSON>u<PERSON><PERSON>r mes sanctions", "OuvrirMesRetoursAbsences": "Ou<PERSON><PERSON>r mes retours d'absences", "OuvrirMaDemandeMission": "<PERSON><PERSON><PERSON><PERSON>r demande de mission", "AjouterDemandeMission": "Ajouter demande de mission", "OuvrirMesRetoursDeMission": "<PERSON><PERSON><PERSON><PERSON>r mes retours de mission", "OuvrirMaDemandePret": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "AjouterDemandePret": "A<PERSON><PERSON> demand<PERSON> de <PERSON>", "OuvrirMonBulletins": "Ouvrir mes bulletins", "OuvrirMesConvocationMedicales": "Ouvrir mes convocations médicales", "OuvrirValiderDocumentManager": "Ouvrir valider document manager", "RejeterDocument": "Rejeter document manager", "ValiderDocument": "Valider document manager", "OuvrirTousLesDocumentsManager": "<PERSON><PERSON><PERSON><PERSON><PERSON> tous les documents manager", "OuvrirEtatGlobalObjectifsManager": "<PERSON>u<PERSON><PERSON><PERSON> état global objectifs manager", "OuvrirEtatGlobalDeRealisationsManager": "<PERSON>uv<PERSON>r état global réalisations manager", "OuvrirRapportRealisationsManager": "Ouvrir rapports réalisations manager", "OuvrirNoteDisciplinaireManager": "Ouvrir note disciplinaire manager", "AjouterNoteDisciplinaireManager": "Ajouter note disciplinaire manager", "OuvrirSanctionManager": "<PERSON>uv<PERSON>r sanction manager", "AjouterSanctionManager": "Ajouter sanction manager", "OuvrirRetourEffectifManager": "<PERSON><PERSON><PERSON><PERSON><PERSON> retour effectif manager", "ParametresGeneraux": "Paramètres généraux", "ParametresEvaluation": "Paramètres d'évaluation", "MesOperations": "Mes opérations", "Comptabilite": "Comptabilité", "OperationManager": "Opération manager", "OperationRH": "Opération RH", "Pret": "<PERSON><PERSON><PERSON><PERSON>", "Medecine": "Médecine", "OuvrirValiderDocuments": "Ouvrir valider documents", "ValiderDocumentRh": "Valider document RH", "RejeterDocumentRh": "Rejeter document RH", "OuvrirRhTousLesDocuments": "Ouvrir tous les documents RH", "OuvrirRhEtatGlobalObjectifs": "Ouvrir état global objectifs RH", "OuvrirEtatRhGlobalRealisations": "Ouvrir état global réalisations RH", "OuvrirAttentesChezManagers": "<PERSON><PERSON><PERSON><PERSON><PERSON> attentes chez managers", "OuvrirRhRapportRealisation": "Ouvrir rapport réalisations RH", "OuvrirRepportingRealisation": "Ouvrir reporting réalisation RH", "OuvrirRapports": "Ouvrir rapports", "OuvrirNoteDisciplaire": "Ouvrir note disciplinaire", "OuvrirRHSanction": "Ouvrir sanction RH", "AjouterRhSanction": "Ajouter sanction RH", "ModifierRhSanction": "Modifier sanction RH", "OuvrirStatsConges": "Ouvrir statistiques congés", "OuvrirGenerationNormeEtObjectids": "Ouvrir génération normes et objectifs", "GenererLesDocumentsNormeEtObjectifs": "Générer les documents (normes et objectifs)", "OuvrirRecapitulatifEvaluation": "Ouvrir récapitulatif des évaluations", "GenererRecapitulatifEvaluation": "Générer récapitulatif des évaluations", "OuvrirEmploye": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "AjouterEmploye": "<PERSON><PERSON><PERSON>", "ImprimerEmploye": "Imprimer employé", "ExporterListeEmploye": "Exporter la liste des employés", "OuvrirStagiaires": "<PERSON><PERSON><PERSON><PERSON><PERSON> stag<PERSON>", "AjouterStagiaires": "Ajouter stagiaires", "OuvrirBilanSocial": "<PERSON><PERSON><PERSON><PERSON>r bilan social", "OuvrirAffectations": "Ouvrir affectations", "AjouterAffectations": "Ajouter affectations", "TelechargerAffectations": "Télécharger affectations", "OuvrirContrat": "<PERSON><PERSON><PERSON><PERSON><PERSON> contrat", "AjouterContrat": "Ajouter contrat", "TelechargerContrat": "Télécharger contrat", "OuvrirEtapesRecrutements": "<PERSON><PERSON><PERSON><PERSON>r <PERSON> de recrutements", "AjouterEtapesRecrutements": "Ajouter étapes de recrutements", "TelechargerEtapesRecrutements": "Télécharger étapes de recrutements", "OuvrirBesoinEnRecrutement": "<PERSON><PERSON><PERSON><PERSON><PERSON> besoin en recrutement", "AjouterBesoinEnRecrutement": "A<PERSON>ter besoin en recrutement", "OuvrirCampagnesRecrutement": "<PERSON><PERSON><PERSON><PERSON><PERSON> campagnes de recrutement", "ModifierCampagnesRecrutement": "Modifier campagnes de recrutement", "OuvrirPvsRecrutement": "Ouvrir PV de recrutement", "NoterLesCandidatsParEtape": "Noter les candidats par étape", "OuvrirCandidats": "<PERSON><PERSON><PERSON><PERSON><PERSON> candidats", "SupprimerCandidats": "<PERSON><PERSON><PERSON><PERSON> candidats", "OuvrirCandidatures": "<PERSON><PERSON><PERSON><PERSON><PERSON> candidatures", "ValiderCandidatures": "Valider candidatures", "OuvrirPostes": "<PERSON><PERSON><PERSON><PERSON><PERSON> postes", "AjouterPostes": "Ajouter postes", "OuvrirPlanningRecrutement": "Ouvrir planning de recrutement", "OuvrirDerogation": "<PERSON><PERSON><PERSON><PERSON><PERSON> d<PERSON>ation", "RejeterDerogation": "<PERSON><PERSON><PERSON> d<PERSON>", "Deroger": "<PERSON><PERSON><PERSON><PERSON>", "OuvrirReinitialisationDocument": "Ouvrir réinitialisation document", "DerogerDocument": "Déroger document", "OuvrirGestionUtilisateurs": "Ouvrir gestion des utilisateurs", "OuvrirProfil": "<PERSON><PERSON><PERSON><PERSON><PERSON> profil", "AjouterProfil": "Ajouter profil", "DupliquerProfil": "Dupliquer profil", "OuvrirNiveaux": "<PERSON><PERSON><PERSON><PERSON><PERSON> ni<PERSON>aux", "AjouterNiveaux": "A<PERSON><PERSON> niveaux", "OuvrirUtilisateurs": "<PERSON><PERSON><PERSON><PERSON>r utilisateurs", "AjouterUtilisateurs": "Ajouter utilisateur(s)", "ValiderUtilisateurs": "Valider utilisateur(s)", "SuspendreUtilisateurs": "<PERSON><PERSON><PERSON><PERSON> utilisateur(s)", "ActiverUtilisateurs": "Activer utilisateur(s)", "ResetPassword": "Réinitialiser le mot de passe", "OuvrirAutorisations": "Ouvrir autorisations", "ModifierAutorisations": "Modifier autorisations", "OuvrirSessions": "Ouvrir sessions", "OuvrirLangues": "<PERSON><PERSON><PERSON><PERSON><PERSON> langues", "AjouterLangues": "Ajouter langues", "OuvrirPays": "Ouvrir pays", "AjouterPays": "Ajouter pays", "OuvrirPeriode": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SauvegarderPeriode": "Sauvegarder pé<PERSON>de", "OuvrirGroupesRubrique": "Ouvrir groupes de rubriques", "AjouterGroupesRubique": "Ajouter groupes de rubriques", "OuvrirFonctionPaie": "Ouvrir fonction paie", "AjouterFonctionPaie": "Ajouter fonction paie", "OuvrirRubriques": "Ouvrir rubriques", "AjouterRubriques": "Ajouter rubriques", "OuvrirCalcule": "<PERSON><PERSON><PERSON><PERSON><PERSON> calcul", "GenererRapportPaie": "Générer rapport de paie", "GenererBulletins": "Générer bulletin(s)", "Calculer": "Calculer", "SupprimerLesCalcules": "Supprimer les calculs", "OuvrirAttributionNombreJours": "Ouvrir attribution nombre de jour(s)", "OuvrirPaieInverse": "<PERSON><PERSON><PERSON><PERSON><PERSON> paie inversée", "EffacerPaieInverse": "<PERSON>ff<PERSON>r paie invers<PERSON>", "SimulerPaieInverse": "Simuler paie inversée", "OuvrirMotifSeparation": "Ouvrir motif de séparation", "AjouterMotifSeparation": "Ajouter motif de séparation", "OuvrirFinRelation": "Ouvrir fin de relation", "AjouterFinRelation": "Ajouter fin de relation", "OuvrirTableauDeBord": "<PERSON><PERSON><PERSON><PERSON><PERSON> tableau de bord", "OuvrirAnalyseEnCours": "<PERSON>u<PERSON><PERSON>r analyse en cours", "ValidationDesCalculs": "Validation des calculs", "OuvrirBulletin": "Ouvrir bulletin", "SupprimerDernierBulletin": "Supprimer dernier bulletin", "ExporterBulletins": "Exporter bulletin(s)", "OuvrirGenerationDipeMagnetic": "Ouvrir génération de dipes magnétiques", "AjouterGenerationDipeMagnetic": "Ajouter génération de dipes magnétiques", "OuvrirParametrePaie": "<PERSON><PERSON><PERSON><PERSON><PERSON>e", "SauvegarderParametrePaie": "Sauvegarder paramè<PERSON> de <PERSON>aie", "OuvrirBilanSalarial": "<PERSON><PERSON><PERSON><PERSON><PERSON> bilan salarial", "VoirBilanSalarial": "Voir bilan salarial", "OuvrirReports": "Ouvrir rapports", "OuvrirGenererDocumentsContributions": "<PERSON><PERSON><PERSON><PERSON><PERSON> g<PERSON> documents de contributions", "ImprimerDocumentsContributions": "Imprimer documents de contributions", "OuvrirChapitre": "<PERSON><PERSON><PERSON><PERSON><PERSON> chapitre", "AjouterChapitre": "Ajouter chapitre", "OuvrirCompteAgence": "Ouv<PERSON>r compte agence", "ModifierCompteAgence": "Modifier compte agence", "OuvrirCompteDeLiaison": "Ouvrir compte de liaison", "ModifierCompteDeLiaison": "Modifier compte de liaison", "OuvrirRubriqueJournalisation": "Ouvrir rubrique de journalisation", "AjouterRubriqueJournalisation": "Ajouter rubrique de journalisation", "OuvrirTypeCompteEmploye": "Ouvrir type de compte employé", "AjouterTypeCompteEmploye": "Ajouter type de compte employé", "OuvrirEcritureComptable": "O<PERSON><PERSON><PERSON>r écriture comptable", "GenererEcritureComptable": "Générer les écritures comptables", "OuvrirTypePret": "Ouvrir type de prêt", "AjouterTypePret": "Ajouter type de prêt", "OuvrirListePret": "<PERSON><PERSON><PERSON><PERSON><PERSON> liste de <PERSON>rê<PERSON>", "ExporterListePret": "Exporter liste de prêts", "ModifierPret": "Modifier prêt", "SuspendrePret": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "OuvirListeActionsPrets": "<PERSON><PERSON><PERSON><PERSON><PERSON> liste d'actions de prêts", "OuvrirMedecins": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "AjouterMedecins": "<PERSON><PERSON><PERSON> mé<PERSON>s", "OuvrirHopitaux": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "AjouterHopitaux": "<PERSON><PERSON><PERSON>", "OuvrirLaboratoiresAnalyses": "Ouvrir laboratoires d’analyses", "AjouterLaboratoiresAnalyses": "Ajouter laboratoires d’analyses", "OuvrirTypeDeVisite": "Ouvrir type de visite", "AjouterTypeDeVisite": "Ajouter type de visite", "OuvrirExamensMedicaux": "<PERSON><PERSON><PERSON><PERSON><PERSON> examens mé<PERSON>", "AjouterExamensMedicaux": "Ajouter examens médicaux", "OuvrirSpecialisations": "Ouvrir spécialisations", "AjouterSpecialisations": "Ajouter spécialisations", "OuvrirConvocationsMedicales": "Ouv<PERSON>r convocations médicales", "AjouterConvocationsMedicales": "Ajouter convocations médicales", "OuvrirConfigurationConge": "Ouvrir configuration de congé", "AjouterConfigurationConge": "Ajouter configuration de congé", "OuvrirPlanningCongeAnnuel": "Ouvrir planning congé annuel", "OuvrirPlanningCongesValides": "Ouvrir planning congés validés", "OuvrirListeAbsences": "<PERSON><PERSON><PERSON><PERSON><PERSON> liste absences", "OuvrirTypeSanction": "Ouvrir type de sanction", "AjouterTypeSanction": "Ajouter type de sanction", "OuvrirTypeNoteDisciplinaire": "Ouvrir type de note disciplinaire", "AjouterTypeNoteDisciplinaire": "Ajouter type de note disciplinaire", "OuvrirFaute": "<PERSON><PERSON><PERSON><PERSON><PERSON> faute", "AjouterFaute": "<PERSON><PERSON><PERSON> faute", "OuvrirListeDesSanctions": "Ouvrir liste des sanctions", "OuvrirFraisMission": "<PERSON>u<PERSON><PERSON>r frais de mission", "AjouterFraisMission": "Ajouter frais de mission", "OuvrirRubriqueMission": "Ouvrir rubrique de mission", "AjouterRubriqueMission": "Ajouter rubrique de mission", "OuvrirConfigurationMission": "Ouvrir configuration de mission", "AjouterConfigurationMission": "Ajouter configuration de mission", "OuvrirPlanningMissionValide": "Ouvrir planning mission validé", "OuvrirNiveauFormation": "Ouvrir niveau de formation", "AjouterNiveauFormation": "Ajouter niveau de formation", "OuvrirEntrepriseFormatrice": "Ouvrir entreprise formatrice", "AjouterEntrepriseFormatrice": "Ajouter entreprise formatrice", "OuvrirTypeFormation": "Ouvrir type de formation", "AjouterTypeFormation": "Ajouter type de formation", "OuvrirCategorieFormation": "Ouvrir catégorie de formation", "AjouterCategorieFormation": "Ajouter catégorie de formation", "OuvrirElementFacturation": "Ouvrir élément de facturation", "AjouterElementFacturation": "Ajouter élément de facturation", "OuvrirOffreFormation": "Ouvrir offre de formation", "AjouterOffreFormation": "Ajouter offre de formation", "OuvrirSessionFormation": "Ouvrir session de formation", "AjouterSessionFormation": "Ajouter session de formation", "AjouterEmployeEnSessionFormation": "Ajouter employé en session de formation", "ListerParticipantEnSessionFormation": "Lister les participants à une session de formation", "ListeEmployeAbsentEnSessionFormation": "Lister les employés absents à une session de formation", "BudgetisationSessionFormation": "Budgétisation de la session de formation", "HistoriquePaiementSessionFormation": "Historique de paiement de la session de formation", "CritereEvaluationSessionFormation": "Critère d'évaluation d'une session de formation", "OuvrirBesoinFormation": "<PERSON><PERSON><PERSON><PERSON><PERSON> besoin de formation", "OuvrirPlanningFormation": "Ouvrir planning de formation", "GestionPoste": "Gestion de poste", "OuvrirFormation": "Ouvrir formation", "OuvrirDiplome": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "OuvrirTache": "<PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>", "OuvrirCompetence": "<PERSON><PERSON><PERSON><PERSON><PERSON> compétence", "OuvrirFiliere": "<PERSON><PERSON><PERSON><PERSON><PERSON> fili<PERSON>", "AjouterTache": "<PERSON><PERSON><PERSON> tâche", "AjouterFormation": "Ajouter formation", "AjouterDiplome": "<PERSON><PERSON><PERSON>", "AjouterCompetence": "Ajouter compétence", "AjouterFiliere": "A<PERSON>ter filière", "Contact_d_urgence": "Contacts d'urgence", "Critere_de_notation": "Critère de notation", "AjouterCritereNotationFormation": "Ajouter critère de notation", "OuvrirCritereNotationFormation": "Ouvrir critère de notation", "critere_Notation": "Critère de notation", "Liste_des_critere_notation": "Critères de notation", "Planning_Formation": "Planning de formation", "Formation_A_Venir": "Formation à venir", "Formation_En_Cours": "Formation en cours", "Formation_Terminer": "Formation terminée", "Nombre_de_participants_Absents": "Nombre de participants absents", "Marquer_Absent": "<PERSON><PERSON> absent", "Mes_sessions_de_formations": "Mes sessions de formation", "Tableau des aptitudes": "Aptitudes", "Tableau des experiences": "Expériences", "Tableau des autres": "Autres", "Profile Info": "Informations du profil", "Profile Authorities": "Habilitations du profil", "You are about to reset the password of this user": "Réinitialisation du mot de passe de cet utilisateur", "Reset user password": "Réinitialiser mot de passe utilisateur", "You are about to reset the password of these users": "Réinitialiser ces comptes utilisateurs", "Confirm the suspend of this user": "Su<PERSON><PERSON><PERSON> le compte de cet utilisateur", "Suspend user": "Su<PERSON><PERSON>re compte utilisateur", "Confirm the suspend of these users": "Su<PERSON>endre ces comptes utilisateurs", "Confirm the activation of this user": "Activer le compte de cet utilisateur", "Confirm the activation of these users": "Activer ces comptes utilisateurs", "Activate user": "Activer compte utilisateur", "Validate account of this user": "Valider le compte de cet utilisateur", "Validate user account": "Valider compte utilisateur", "Validate account of these users": "Valider ces comptes utilisateurs", "Note_disciplinaires_attente": "Notes disciplinaires en attente", "Mission(s)_effectuée(s)": "Missions effectuées", "Montant_Pret_total_encours": "Montant total des prêts en cours", "Nombre_pret_total": "Nombre total de prêts", "Nombre_total_pret_remboursé": "Nombre total de prêts remboursés", "Montant_pret_restant": "<PERSON><PERSON> du <PERSON>rêt restant", "Note(s)_disciplinaire(s)": "Notes disciplinaires", "Pourcentage_adequation_poste": "Pourcentage d'adéquation poste", "Évaluation": "Évaluation", "Doc_type_realisation": "Nombre total de documents de type réalisation", "Critere_Notation": "Critère de notation", "Noter_la_formation": "Noter la formation", "Envoyer_En_Formation": "Envoyer en formation", "Liste_besoin_formation": "Liste des besoins en formation relevés chez l'employé", "Montant_total": "Montant total", "Critere_notation": "Critère de notation", "MOYENNE_DE_LA_FORMATION_/_20": "MOYENNE DE LA FORMATION / 20", "Element_facturation": "Élément de facturation", "note_Des_Employes": "Notes des employés", "Notes": "Notes", "Fiches_VALIDER": "Documents de type réalisation validés", "Fiches_REJETER": "Documents de type réalisation rejetés", "nombre_noteDisciplinaire_send_by_manager": "Nombre de notes disciplinaires envoyées", "Note_disciplinaires_en_attente_justificatif_send_by_manager": "Nombre de notes disciplinaires en attente", "Note_disciplinaires_justifier_send_by_manager": "Nombre de notes disciplinaires justifiées", "DOc_en_attente_validation_RH": "Documents en attente de validation RH", "DOc_en_attente_validation_Manager": "Documents en attente de validation chez les managers", "HR_dashboard": "Tableau de bord RH", "Employee_dashboard": "Tableau de b<PERSON>é", "Manager_dashoard": "Tableau de bord manager", "mes_pret": "<PERSON><PERSON>", "note_disciplinaires_manager": "Notes disciplinaires en attente", "Nbre_Conge_annuel": "Nombre de jours de congé annuel", "Nbre_Autre_type_conge": "Nombre de jours de congé d'autre type", "Demande_pret": "<PERSON><PERSON><PERSON>", "nombre_noteDisciplinaire_send_by_rh": "Nombre de notes disciplinaires envoyées", "Absence_type": "Absences par type", "Note_Disciplinaire_type": "Notes disciplinaires par type", "Employee_not_attached_employee": "Cet utilisateur n'est pas encore rattaché à un employé", "Taux_adéquation_formation": "Taux d'adéquation formation", "Confirmation_date": "Date de confirmation", "Nbre_fiche": "Nombre de fiche(s)", "Mois_ouvre": "<PERSON><PERSON> ouv<PERSON>", "Statistique_evaluation": "Statistique d'évaluation", "Confirmer_le": "Confirmer le", "Nombre_fiches": "Nombre de fiche(s)", "Confirmation": "Confirmation", "Autre": "<PERSON><PERSON>", "Et_le": "Et le", "Type_mission": "Type de mission", "date_RetourEffectif": "Date de retour effectif", "Fonction_Evaluable": "Fonction évaluable", "Santion_par_type": "Sanctions par type", "Sanction_noteDisciplinaire": "Sanctions & notes disciplinaires", "Salaire": "Salaire", "Absence_conge": "Absences & congés", "CriteresParFonction.Null": "Configurer les critères par fonction", "fonctionEvaluable": "Fonction évaluable", "Nombre_fonction_NonConfig": "Fonctions non configurées", "Taux_majoration": "Taux de majoration", "Inferieure_la_valeur_minimal": "Inférieur à la valeur minimale", "Cette affectaion ne contient aucune note de service": "Cette affectation ne contient aucune note de service", "Ce contrat ne contient aucun fichier": "Ce contrat ne contient aucun fichier", "Manager_data": "Informations du manager", "Duplicate_entry": "Ce critère existe déjà", "Fonction_configurer": "Fonction non configurée", "Valeur.Null": "Entrez les valeurs des objectifs manquants", "Nombre_total_sanction": "Nombre total de sanctions", "Nombre_total_pret_suspendu": "Nombre total de prêts suspendus", "Mes_demande_pret": "<PERSON><PERSON> demand<PERSON>(s)", "Mes_Prets": "<PERSON><PERSON>(s)", "Stat_Absence": "Statistique des congés", "Document.Objectifs.EnCours.Exist": "Des objectifs sont déjà en cours.", "Voulez-vous mettre en essaie ce candidat?": "Vou<PERSON><PERSON>-vous mettre ce candidat en essai ?", "STD_CANCEL": "Annuler", "EDataUploadType.LissageCritereParFonction": "Lissage des objectifs par fonction", "EDataUploadType.LissageCritereParMatricule": "Lissage des objectifs par matricule", "Saisir_objectif": "Saisir votre objectif", "Saisie Obligatoire?": "Saisie des justificatifs obligatoire ?", "Saisir_realisation": "Saisir votre réalisation", "ConsulterDashBoardEmploye": "Consulter dashboard employé", "ConsulterDashBoardManager": "Consulter dashboard manager", "EMotifRadier.cessationContrat": "Fin de contrat", "Rapport_evaluation": "Rapport d'évaluation", "Rapports_configuration_evaluation": "Rapports de la configuration de l'évaluation", "Date_naissance": "Date de naissance", "Modification_informations_personnelles": "Modification de mes informations personnelles", "Objectif_minimal": "Objectif <PERSON>", "Votre_document_sera_envoye_en_derogation": "Votre document sera envoyé en dérogation", "Liste.Objectifs.Vide": "La liste des objectifs est vide.", "Saisir_valeur_non_decimale": "Sai<PERSON><PERSON>z une valeur non décimale", "Valeur_realisation": "Valeur de votre réalisation", "select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Element.De.Controle.Null": "Ajoutez des éléments de contrôle dans la liste", "No.Value.property": "Veuillez configurer un élément de contrôle de type 'Nombre' comme valeur de réalisation", "Many.Value.properties": "Veuillez configurer un seul élément de contrôle comme valeur de réalisation", "Type.Donnee.Propriete.Invalid": "Veuillez configurer un élément de contrôle de type 'Nombre' comme valeur de réalisation", "Ajouter_une_photo": "Ajouter une photo", "Saisir.Une.Valeur.Positive.Realisation": "Sai<PERSON><PERSON>z une valeur positive pour votre réalisation !", "Document.Norme_Objectif.NotFound": "Vous n'avez pas reçu d'objectif, rapprochez-vous de la DRH pour en recevoir", "GenerateRealisation.Objectif.NotFound": "Votre liste d'objectifs est vide, rapprochez-vous de la DRH pour en recevoir", "Document.Norme_Objectif.Not.Validated": "Vos objectifs n'ont pas encore été validés par la DRH, veuillez attendre leur validation avant de saisir vos réalisations", "Evaluable?": "Évaluable ?", "Nom_personne": "Nom du contact", "Demande_modification_informations": "Demande de modification des informations de l'employé", "Another.Conge.In.This.Period.Exist": "Un autre congé existe déjà dans cette période", "Conge.Already.Exist": "Une demande de congé similaire existe déjà", "Departement": "Département", "Nombre_Jours_Autorises": "Nombre de jour(s) autorisé(s)", "Mot_reastant": "Mot(s) restant(s)", "AjouterRhNoteDisciplinaire": "Ajouter une note disciplinaire RH", "Generer rapport de paie": "Générer rapport de paie", "Paie_conge": "<PERSON><PERSON> con<PERSON>", "Compte Employe": "Comptes bancaires des employés", "rattache_note_disciplinaire": "Rattacher à une note disciplinaire", "Nbr_Fractionnement": "Nombre de fractionnements", "Absence_conge_mission": "Absences, congés et missions", "EDataUploadType.MailPro": "Adresse e-mail pro", "ChangePassword.Password.Format.Invalid": "Votre mot de passe ne respecte pas le format requis", "Password.Format.NbNumeric.Less": "Votre mot de passe ne contient pas le nombre minimum de chiffres requis", "Liste_des_demandes_de_modification_de_profile": "Liste des demandes de modification de profil", "consulter": "Consulter", "Date_de_demande_de_modification": "Date de la demande de modification", "fichier_joints": "Fichiers joints", "Password.Format.NbSpecialChars.Less": "Votre mot de passe ne contient pas le nombre minimum de caractères spéciaux requis", "Password.Format.NbUpperChars.Less": "Votre mot de passe ne contient pas le nombre minimum de majuscules requis", "EDataUploadType.CritereParFonction": "Critère par fonction", "Nombre_echeance_restant": "Nombre d'échéances restantes", "EMotifRadier.AbandonDePoste": "Abandon de poste", "EMotifRadier.SuspensionDeContrat": "Suspension de contrat", "EMotifRadier.RuptureEssaie": "Rupture période d'essai", "EMotifRadier.EssaiNonConcluant": "Essai non concluant", "EMotifRadier.SeparationAmiable": "Séparation à l'amiable", "Date_saisi": "Date de saisie", "Date_butoire": "Date butoir", "Type_document": "Type de document", "parametre_rubrique_a_reinitialiser": "Rubrique à réinitialiser après clôture de la paie", "norme_objectif": "Document des normes et objectifs", "ETypeDocument.modification_infos_profile": "Modification des informations du profil", "ENREGISTRE": "Enregistré", "TERMINE": "<PERSON><PERSON><PERSON><PERSON>", "CLOTURE": "Clôturé", "norme_objectif_archives": "Document des normes et objectifs archivés", "realisation": "Document des réalisations", "realisation_archives": "Document des réalisations archivés", "conge": "De<PERSON>e d'absences/congés", "mission": "<PERSON><PERSON><PERSON> de <PERSON>", "demande_formation": "Demande de formation", "demande_formation_collective": "Demande de formation collective", "demande_remboursement_formation": "Demande de remboursement de formation", "modification_infos_profile": "Demande de modification des informations de l'employé", "DEMANDE_MOBILITE": "<PERSON><PERSON>e de mobilité", "EActionDerogation.ENVOI": "Envoi chez le manager", "EActionDerogation.VALIDATION": "Validation du manager", "EActionDerogation.REJET_VALIDATION": "Rejet du manager", "EActionDerogation.TRANSFERT": "<PERSON>t vers un autre manager", "EActionDerogation.REJET_TRANSFERT": "Rejet du transfert vers un autre manager", "Document.Norme_Objectif.Not.Validated.Manager": "Vos objectifs n'ont pas encore été validés par votre manager, veuillez attendre sa validation, puis celle de la DRH avant de saisir vos réalisations", "Document.Norme_Objectif.Not.Send": "Veuillez envoyer vos objectifs en validation chez votre manager avant de saisir vos réalisations", "Document.Norme_Objectif.Not.Derogation": "Vos objectifs sont en attente de dérogation, veuillez attendre l'accord de la DRH avant de saisir vos réalisations", "Matricule/login": "Matricule/login", "Saisie.Realisation.Indisponible.Pour.Ce.Mois": "La saisie des réalisations n'est pas encore disponible pour ce mois", "Ce_document_est_déjà_présent_dans_le_tableau": "Ce document est déjà présent dans le tableau", "Veuillez_importer_un_document": "Veuillez importer un document", "Generer_Ecriture_Comptable": "Générer les écritures comptables", "Analyse_paie": "<PERSON><PERSON><PERSON> de <PERSON>e", "Fichiers_joints": "Fichiers joints", "Montant_Imposable": "Montant imposable", "employes_en_relation": "Employés en relation", "Imprimer_bulletin_annuel": "Imprimer mon bulletin annuel", "Payslips.Not.Found": "Bulletin(s) non trouvé(s)", "Exercice.Not.Found": "Exercice non trouvé", "Info_employe": "Informations de l'employé", "Valider_nouvel_employe": "Valider le nouvel employé", "ValiderNouvelEmploye": "Valider un nouvel employé", "GENERATE_ECRITURE_COMPTABLE_TITLE": "Générer les écritures comptables", "GENERATE_ECRITURE_COMPTABLE_CONTENT": "Veuillez sélectionner un format", "hint_afficherAllocationConge": "Afficher le cumul de l’allocation congé", "afficherAllocationConge": "Afficher le cumul de l’allocation congé", "IntervalleDebut": "Date de début de l'intervalle", "IntervalleFin": "Date de fin de l'intervalle", "Rapport_paie": "Rapport de paie", "Etat_rubrique": "État rubrique", "CREDIT_FONCIER": "<PERSON><PERSON><PERSON> foncier", "Repporting_paie": "Reporting de la paie", "Liste des typeComptes": "Liste des types de comptes des employés", "CLOTURE_PARTIELLE": "Clô<PERSON> partielle", "Cloture_partielle": "Clô<PERSON> partielle", "CloturePartielleExercices": "Clôturer partiellement l’exercice", "Envoi.Validation.Impossible": "Envoi en validation de votre document impossible", "CreateDocRealisation.Exercice.Not.Valid": "Création du document de réalisation impossible car l'exercice est clôturé.", "CreateDocObjectif.Exercice.Not.Valid": "Création du document des normes et objectifs impossible car l'exercice est clôturé.", "Voulez vous cloturer partielle cette exercice ? La saisie des documents de normes objectifs et de réalisations ne sera plus possible": "Voulez-vous clôturer partiellement cet exercice ? La saisie des documents de normes, d'objectifs et de réalisations ne sera plus possible", "GenerationDipeMagnetic": "Génération des dipes magnétiques", "Etat_contribution": "État sur les contributions", "Fiche_mensuelle": "Fiche mensuelle", "Synthese": "Synthèse", "Synthese_provisoire": "Synthèse provisoire", "Premier_emploi?": "Premier emploi ?", "recapitulatif_organisation": "Récapitulatif organisationnel", "Absence_conge_mission_campagne_recrutement": "Absences, congés, missions et campagnes de recrutement", "Nombre_campagne_en_cours": "Nombre de campagnes en cours", "Campagne_disponible": "Liste de campagnes de recrutement disponibles", "Nombre de place(s)": "Nombre de place(s)", "item(s) total": "Élément(s) total", "Items per page:": "Éléments par page :", "Type_campagne": "Type de campagne", "FIRST": "Premier", "PREVIOUS": "Précédent", "NEXT": "Suivant", "LAST": "<PERSON><PERSON>", "ARIA_FIRST": "Aller à la première page", "ARIA_PREVIOUS": "Aller à la page précédente", "ARIA_NEXT": "Aller à la page suivante", "ARIA_LAST": "Aller à la dernière page", "Code_rubrique": "Code rubrique", "Nom_Prenom": "Nom & prénom", "Certificat_medical": "Certificat médical", "Date_fin_contrat": "Date de fin de contrat", "Configuration_absence": "Configuration des absences", "parametre_implementation_loi_jeune": "Paramètrage de l'implémentation de la loi emploi jeune", "activer_loi_jeune": "Activer la loi emploi jeune", "NombreAnneeLoiEmploiJeune": "Durée d'application de la loi emploi jeune (nombre d'années) ", "ageMaxLoiEmploiJeune": "Age maximum d'application de la loi emploi jeune", "Selectionner_une_rubrique_exonere_par_la_loi_emploi_jeune": "Sélectionner une rubrique exonérée par la loi emploi jeune", "hint_premier_emploi": "Est ce le premier emploi de cet employé ?", "Loi_Emploi_Jeune": "Loi emploi jeune", "Parametrage_loi_emploi_jeune": "Paramétrage de la loi emploi jeune", "Age.Max.Null": "L'âge maximum est vide", "Age.Max.Range.Invalid": "L'âge maximum doit être compris entre 0 et 100", "Nbr.Annee.Null": "Le nombre d'années est vide", "Nbr.Annee.Range.Invalid": "Le nombre d'années doit être compris entre 0 et 100", "Rubriques.Empty": "La liste des rubriques exonérées est vide", "Rubriques.Must.Include.Patronale": "La liste des rubriques exonérées doit uniquement avoir des rubriques patronales", "Parametres_loi_fiscales": "Paramètres loi fiscale", "Connexion_securisee": "Connexion sécurisée SSL", "Conseils_pour_un_mot_de_passe_securisee": "Conseils pour un mot de passe sécurisé", "Au_moins_8_caracteres": "Au moins 8 caractères", "Melange_de_majuscules_et_minuscules": "Mélange de majuscules et minuscules", "Inclure_des_chiffres_et_symboles": "Inclure des chiffres et symboles", "Eviter_les_informations_personnelles": "É<PERSON>ter les informations personnelles", "Force_du_mot_de_passe_:": "Force du mot de passe :", "Caracteres": "Caractères", "Force_du_mot_de_passe_": "Force du mot de passe :", "Mot_de_passe_très_faible": "Mot de passe très faible", "Mot_de_passe_faible": "Mot de passe faible", "Mot_de_passe_moyen": "Mot de passe moyen", "Mot_de_passe_bon": "Mot de passe fort", "Mot_de_passe_excellent": "Mot de passe très fort", "Utilisez_au_moins_8_caractères": "Utilisez au moins 8 caractères", "Ajoutez_des_lettres_minuscules": "Ajou<PERSON>z des lettres minuscules", "Ajoutez_des_lettres_majuscules": "Ajou<PERSON>z des lettres majuscules", "Ajoutez_des_chiffres": "Ajoutez des chiffres", "Ajoutez_des_caracteres_speciaux": "Ajoutez des caractères spéciaux (!@#$%^&*)", "Evitez_les_caracteres_repetitifs": "Évitez les caractères répétitifs", "Evitez_les_sequences_communes": "Évitez les séquences communes (123, abc, qwe, asd, zxc, etc.)", "Les_mots_de_passe_ne_correspondent_pas": "Les mots de passe ne correspondent pas", "Les_mots_de_passe_correspondent": "Les mots de passe correspondent"}