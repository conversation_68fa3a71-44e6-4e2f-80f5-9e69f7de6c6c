package org.pkfrc.ehr.repo.paie;

import java.util.List;

import org.pkfrc.ehr.entities.paie.EEtatPret;
import org.pkfrc.ehr.entities.paie.Pret;
import org.pkfrc.ehr.entities.paie.TypePret;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface PretRepository extends JpaRepository<Pret, Long> {

	// Pret findByEmploye_IdAndTypePret_code(Long id, String code);

	List<Pret> findByEmployeMatriculeAndTypePretId(String matricule, Long idPret);

	List<Pret> findByExercice(String exercice);

	Pret findByCode(String code);

	Pret findById(Long id);

	@Query("SELECT max(id) FROM Pret")
	Long getMaxID();

	Pret findByEmployeMatricule(String matricule, Long idPret);

	List<Pret> findByEtatPret(EEtatPret etatpret);

	@Query(value = "SELECT MAX(NULLIF(CAST(code AS INTEGER), 0)) " + "FROM pkf_ehr_document "
			+ "WHERE code ~ '^[0-9]+$' AND type_doc = 'pret' ", nativeQuery = true)
	Integer getMaxCode();

	@Query("SELECT p  FROM Pret p INNER JOIN p.employe e" + " WHERE " + " e.matricule = :matricule AND "
			+ " (p.etatPret = org.pkfrc.ehr.entities.paie.EEtatPret.EnCours) AND" + " p.reste > 0 "
			+ " ORDER BY p.dateValidationPret asc")
	List<Pret> getPretEncours(@Param("matricule") String matricule);

	// @Query("SELECT p FROM Pret p INNER JOIN p.employe e" + " WHERE " + "
	// e.matricule = :matricule AND "
	// + " (p.etatPret = org.pkfrc.ehr.entities.paie.EEtatPret.EnCours) AND" + "
	// p.reste > 0 AND"
	// + " P.typePret = type"
	// + " ORDER BY p.dateValidationPret asc")
	// List<Pret> getPretEncoursParType(@Param("matricule") String
	// matricule,@Param("type") TypePret type);

	List<Pret> findByEmployeMatriculeAndEtatPretAndTypePret(String matricule, EEtatPret etat, TypePret type);

	// @Query("SELECT p FROM Pret p"
	// + " WHERE "
	// + " (p.etatPret = org.pkfrc.ehr.entities.paie.EEtatPret.EnCours OR p.etat =
	// org.pkfrc.ehr.entities.evaluation.document.EEtatDocument.VALIDERRH ) AND"
	// + " p.reste > 0 "
	// + " ORDER BY p.dateValidationPret asc")
	// List<Pret> getPretEncours();

	/* Montant total et nombre total pret encours */
	@Query("SELECT SUM(p.montant) as montantTotal, COUNT(p.montant) as nombreTotal FROM Pret p INNER JOIN p.employe e"
			+ " WHERE e.matricule = :matricule")
	List<Object[]> getMontantEtNombreTotalPret(@Param("matricule") String matricule);
	/* Fin Montant total et nombre total pret encours */

	/* Montant total et nombre total pret EnCours */
	@Query("SELECT SUM(p.montant) as montantTotal, COUNT(p.montant) as nombreTotal FROM Pret p INNER JOIN p.employe e"
			+ " WHERE e.matricule = :matricule AND p.etatPret = org.pkfrc.ehr.entities.paie.EEtatPret.EnCours")
	List<Object[]> getMontantEtNombreTotalPretEncours(@Param("matricule") String matricule);
	/* Fin Montant total et nombre total pret EnCours */

	/* Montant total et nombre total pret Restant EnCours */
	@Query(value = "SELECT SUM(p.reste) as montantTotal, COUNT(p.reste) as nombreTotal FROM pkf_ehr_pret as p "
			+ "INNER JOIN pkf_ehr_employe as emp ON emp.id_personne = p.employe_id "
			+ "WHERE emp.emp_matricule = :matricule AND p.etat_pret = 'EnCours'", nativeQuery = true)
	List<Object[]> getMontantEtNombreTotalPretRestantEncours(@Param("matricule") String matricule);
	/* Fin Montant total et nombre total pret Restant EnCours */

	// For pret : encours et supendu
	@Query("SELECT SUM(p.montant) as montantTotal, COUNT(p.montant) as nombreTotal FROM Pret p INNER JOIN p.employe e"
			+ " WHERE e.matricule = :matricule AND p.etatPret = :status")
	List<Object[]> getMontantEtNombreTotalGlobalPretEncoursByState(@Param("matricule") String matricule,
			@Param("status") EEtatPret status);

	@Query("SELECT DISTINCT p FROM Pret p " +
			"INNER JOIN p.typePret typ " +
			"INNER JOIN p.remboursements r " +
			"INNER JOIN p.employe e " + "" +
			"WHERE e.matricule = :matricule " +
			"AND typ.code = :codeTypePret " +
			"AND FUNCTION('YEAR', r.dateRemboursement) = :year")
	List<Pret> getPretWithRemboursementInYear(@Param("year") int year, @Param("codeTypePret") String codeTypePret,
			@Param("matricule") String matricule);

	@Query("SELECT DISTINCT p FROM Pret p " +
			"INNER JOIN p.typePret typ " +
			"INNER JOIN p.remboursements r " +
			"INNER JOIN p.employe e " + "" +
			"WHERE e.matricule = :matricule " +
			"AND typ.code = :codeTypePret " +
			"AND FUNCTION('MONTH', r.dateRemboursement) = :month")
	List<Pret> getPretWithRemboursementInMonth(@Param("month") int month, @Param("codeTypePret") String codeTypePret,
			@Param("matricule") String matricule);

	@Query("SELECT CONCAT(e.matricule, typ.code), " +
			"SUM(CASE WHEN p.reste < p.mensualite THEN p.reste ELSE p.mensualite END) " +
			"FROM Pret p INNER JOIN p.employe e INNER JOIN p.typePret typ " +
			"WHERE p.etatPret = :etatPret " +
			"GROUP BY e.matricule, typ.code")
	List<Object[]> getSommeMensualites(@Param("etatPret") EEtatPret etatPret);

	@Query("SELECT CONCAT(e.matricule, typ.code), " +
			"p.id " +
			"FROM Pret p INNER JOIN p.employe e INNER JOIN p.typePret typ " +
			"WHERE p.etatPret = :etatPret " +
			"GROUP BY e.matricule, p.id, typ.code")
	List<Object[]> getIdPretList(@Param("etatPret") EEtatPret etatPret);

	// Consolidated query to get all pret statistics in one call
	@Query("SELECT " +
			"SUM(CASE WHEN p.etatPret = org.pkfrc.ehr.entities.paie.EEtatPret.EnCours THEN p.montant ELSE 0 END) as montantEncours, "
			+
			"COUNT(CASE WHEN p.etatPret = org.pkfrc.ehr.entities.paie.EEtatPret.EnCours THEN 1 END) as nombreEncours, "
			+
			"SUM(CASE WHEN p.etatPret = org.pkfrc.ehr.entities.paie.EEtatPret.Rembourse THEN p.montant ELSE 0 END) as montantRembourse, "
			+
			"COUNT(CASE WHEN p.etatPret = org.pkfrc.ehr.entities.paie.EEtatPret.Rembourse THEN 1 END) as nombreRembourse, "
			+
			"SUM(CASE WHEN p.etatPret = org.pkfrc.ehr.entities.paie.EEtatPret.Suspendu THEN p.montant ELSE 0 END) as montantSuspendu, "
			+
			"COUNT(CASE WHEN p.etatPret = org.pkfrc.ehr.entities.paie.EEtatPret.Suspendu THEN 1 END) as nombreSuspendu, "
			+
			"SUM(p.montant) as montantTotal, " +
			"COUNT(p.montant) as nombreTotal " +
			"FROM Pret p INNER JOIN p.employe e " +
			"WHERE e.matricule = :matricule")
	List<Object[]> getAllPretStatistics(@Param("matricule") String matricule);

}
