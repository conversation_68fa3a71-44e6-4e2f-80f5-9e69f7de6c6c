package org.pkfrc.ehr.service.dashboard;

import java.math.BigInteger;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.log4j.Logger;
import org.pkfrc.core.service.BaseServiceImpl;
import org.pkfrc.core.utilities.enumerations.EOperation;
import org.pkfrc.core.utilities.result.ValidatorResult;
import org.pkfrc.ehr.entities.dashboard.DashboardEmploye;
import org.pkfrc.ehr.repo.recrutement.CampagneRecrutementRepository;
import org.pkfrc.ehr.repo.sanction.SanctionRepository;
import org.pkfrc.ehr.service.absence.IDemandeCongeService;
import org.pkfrc.ehr.service.evaluation.document.IDocNormeObjectifArchivesService;
import org.pkfrc.ehr.service.mission.IDemandeMissionService;
import org.pkfrc.ehr.service.paie.IPretService;
import org.pkfrc.ehr.service.paie.payslip.IPayslipService;
import org.pkfrc.ehr.service.params.generaux.employe.IEmployeService;
import org.pkfrc.ehr.service.sanction.INoteDisciplinaireService;
import org.pkfrc.ehr.service.sanction.ISanctionService;
import org.springframework.stereotype.Service;

@Service
public class DashboardEmployeServiceImpl extends BaseServiceImpl<Long, DashboardEmploye>
		implements IDashboardEmployeService {

	Logger logger = Logger.getLogger(DashboardEmployeServiceImpl.class);

	private final IPretService pretService;
	private final INoteDisciplinaireService noteDisciplinaireService;
	private final ISanctionService sanctionService;
	private final SanctionRepository sanctionRepository;
	private final IDemandeMissionService demandeMissionService;
	private final IPayslipService payslipService;
	private final IDocNormeObjectifArchivesService docNormeObjectifArchivesService;
	private final IEmployeService employeService;
	private final IDemandeCongeService demandeCongeService;
	private final CampagneRecrutementRepository campagneRecrutementRepo;

	public DashboardEmployeServiceImpl(IPretService pretService, INoteDisciplinaireService noteDisciplinaireService,
			ISanctionService sanctionService, SanctionRepository sanctionRepository,
			IDemandeMissionService demandeMissionService, IPayslipService payslipService,
			IDocNormeObjectifArchivesService docNormeObjectifArchivesService, IEmployeService employeService,
			IDemandeCongeService demandeCongeService, CampagneRecrutementRepository campagneRecrutementRepo) {
		this.pretService = pretService;
		this.noteDisciplinaireService = noteDisciplinaireService;
		this.sanctionService = sanctionService;
		this.sanctionRepository = sanctionRepository;
		this.demandeMissionService = demandeMissionService;
		this.payslipService = payslipService;
		this.docNormeObjectifArchivesService = docNormeObjectifArchivesService;
		this.employeService = employeService;
		this.demandeCongeService = demandeCongeService;
		this.campagneRecrutementRepo = campagneRecrutementRepo;
	}

	public DashboardEmploye getInfoDashboardEmploye(String matricule, String exercice) throws Exception {
		DashboardEmploye dashboardEmployeInfo = new DashboardEmploye();
		// Optimized: Use database filtering instead of in-memory filtering
		// int nombreCampagneRecrutement =
		// campagneRecrutementRepo.countActiveExternalCampaigns();

		// Calling the service methods to get the values
		Long employeeMissionValider = demandeMissionService.getEmployeDemandeMisisionValider(matricule, exercice);
		List<Object[]> employeeDocumentsByEtatDoc = docNormeObjectifArchivesService
				.countEmployeeDocumentsByEtatDoc(matricule, exercice);
		Long noteDiscplinaireEnAttenteJustificatif = noteDisciplinaireService
				.getEmployeeNoteDiscplinaireEnAttenteJustificatif(matricule, exercice);
		Double totalDaysRequestedByExerciceAndEmpMatricule = demandeCongeService
				.getTotalDaysRequestedByExerciceAndEmpMatricule(exercice, matricule);
		Double totalDaysRequestedExcludingCongesAnnuel = demandeCongeService
				.getTotalDaysRequestedExcludingCongesAnnuel(exercice, matricule);

		dashboardEmployeInfo.setNombreDemissionEffectuer(employeeMissionValider);
		dashboardEmployeInfo.setEmployeeDocumentsByEtatDoc(employeeDocumentsByEtatDoc);
		dashboardEmployeInfo.setNoteDiscplinaireEnAttenteJustificatif(noteDiscplinaireEnAttenteJustificatif);
		dashboardEmployeInfo
				.setTotalDaysRequestedByExerciceAndEmpMatricule(totalDaysRequestedByExerciceAndEmpMatricule);
		dashboardEmployeInfo.setTotalDaysRequestedExcludingCongesAnnuel(totalDaysRequestedExcludingCongesAnnuel);
		// dashboardEmployeInfo.setNombreCampagneRecrutement(nombreCampagneRecrutement);

		List<Object[]> findDocumentsByTypeExerciceAndMatricule = docNormeObjectifArchivesService
				.findDocumentsByTypeExerciceAndMatricule(matricule, exercice);
		dashboardEmployeInfo.setEmployeeDocRealisationExercice(findDocumentsByTypeExerciceAndMatricule);
		return dashboardEmployeInfo;
	}

	@Override
	public DashboardEmploye getInfoDashboardEmployeForAllExercice(String matricule, String exercice) throws Exception {
		DashboardEmploye dashboardEmployeInfo = new DashboardEmploye();
		List<Object[]> employeeTotalSanctionByTypeSanction = sanctionService
				.getEmployeeTotalSanctionByTypeSanction(matricule);

		dashboardEmployeInfo.setEmployeeTotalSanctionByTypeSanction(employeeTotalSanctionByTypeSanction);
		dashboardEmployeInfo
				.setEmployeeTotalSanction(sanctionRepository.countByEmployeMatricule(matricule));

		Long employeeTotalNoteDisciplinaire = noteDisciplinaireService.getEmployeeNoteDisciplinaire(matricule);
		dashboardEmployeInfo.setEmployeeTotalNoteDisciplinaire(employeeTotalNoteDisciplinaire);

		/*
		 * Double percentageOfMismatchedFormations =
		 * employeService.getPercentageOfMismatchedFormations(matricule);
		 * dashboardEmployeInfo.setPercentageOfMismatchedFormations(
		 * percentageOfMismatchedFormations);
		 */

		List<Object[]> employeeTotalNoteDisciplinaireByTypeNd = noteDisciplinaireService
				.getEmployeeTotalNoteDisciplinaireByTypeNd(matricule);
		dashboardEmployeInfo.setEmployeeTotalNoteDisciplinaireByTypeNd(employeeTotalNoteDisciplinaireByTypeNd);

		// Optimized: Get all pret statistics in one consolidated query
		Map<String, Object> allPretStats = pretService.getAllPretStatisticsConsolidated(matricule);

		// Set all pret-related fields from consolidated result
		dashboardEmployeInfo.setMontantTotalPretEncours((Double) allPretStats.get("montantEncours"));
		dashboardEmployeInfo.setNombreTotalPretEncours((Long) allPretStats.get("nombreEncours"));
		dashboardEmployeInfo.setMontantTotalPretRembourse((Double) allPretStats.get("montantRembourse"));
		dashboardEmployeInfo.setNombreTotalPretRembourse((Long) allPretStats.get("nombreRembourse"));
		dashboardEmployeInfo.setMontantTotalPretSuspendu((Double) allPretStats.get("montantSuspendu"));
		dashboardEmployeInfo.setNombreTotalPretSuspendu((Long) allPretStats.get("nombreSuspendu"));
		dashboardEmployeInfo.setMontantTotalPret((Double) allPretStats.get("montantTotal"));
		dashboardEmployeInfo.setNombreTotalPret((Long) allPretStats.get("nombreTotal"));

		// Note: Remaining amount calculation still needs separate query
		Map<String, Object> totalPretEncoursRestant = pretService.getMontantEtNombreTotalPretRestantEncours(matricule);
		dashboardEmployeInfo.setMontantTotalPretRestant((Double) totalPretEncoursRestant.get("montantTotal"));
		dashboardEmployeInfo.setNombreTotalPretRestant((BigInteger) totalPretEncoursRestant.get("nombreTotal"));

		// lAST 12 Salary evolution
		List<Object[]> monthlySalaryProgression = payslipService.getMonthlySalaryProgression(matricule);
		dashboardEmployeInfo.setMonthlySalaryProgression(monthlySalaryProgression);

		List<Object[]> findEmployeeNbreJrsCongeByIntitule = demandeCongeService.getNbreJrsCongeByIntitule(matricule,
				exercice);
		dashboardEmployeInfo.setEmployeeNbreJrsCongeByIntitule(findEmployeeNbreJrsCongeByIntitule);

		return dashboardEmployeInfo;
	}

	@Override
	protected Logger getLogger() {
		return logger;
	}

	@Override
	protected Class<DashboardEmploye> getClazz() {
		return null;
	}

	@Override
	protected List<ValidatorResult> validateEntity(DashboardEmploye record, EOperation operation) {
		return null;
	}

	@Override
	protected List<ValidatorResult> validateAllEntities(Set<DashboardEmploye> record, EOperation operation) {
		return null;
	}
}
