package org.pkfrc.ehr.repo.params.generaux.employe;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Set;

import org.pkfrc.core.entities.security.User;
import org.pkfrc.ehr.entities.noeud.Noeud;
import org.pkfrc.ehr.entities.noeud.Reseau;
import org.pkfrc.ehr.entities.params.generaux.Fonction;
import org.pkfrc.ehr.entities.params.generaux.employe.Classe;
import org.pkfrc.ehr.entities.params.generaux.employe.ENatureEmploye;
import org.pkfrc.ehr.entities.params.generaux.employe.Employe;
import org.pkfrc.ehr.entities.sanction.enums.ENoteDisciplineStatus;
import org.pkfrc.ehr.entities.utils.enums.ESexe;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface EmployeRepository extends JpaRepository<Employe, Long> {

	Employe findByUser(User user);

	Employe findById(Long id);

	Employe findByMatricule(String matricule);

	long countByPaysNaissanceIsNotNullAndPaysNaissanceIdAndRadie(Integer codeAgence, boolean radie);

	List<Employe> findByAgenceVirementCodeAndRadie(String codeAgence, boolean radie);

	List<Employe> findByAgenceCodeAndRadie(String codeAgence, boolean radie);

	Set<Employe> findByAgenceCodeAndAgenceVirementCodeAndRadie(String codeAgence, String codeAgenceVirement,
			boolean radie);

	List<Employe> findByGradeCodeAndRadie(String codeGrade, boolean radie);

	List<Employe> findByClasseCodeAndRadie(String codeClasse, boolean radie);

	List<Employe> findByFonctionCodeAndRadie(String codeFonction, boolean radie);

	List<Employe> findBySexeAndRadie(ESexe sexe, boolean radie);

	Integer countByAgenceAndRadie(Noeud agence, boolean radie);

	Integer countByAgenceCodeAndSexeAndRadie(String code, ESexe sexe, boolean radie);

	// Integer countByAgenceCodeAndTypeContratAndRadie(String code,EtypeContrat
	// contrat,boolean radie);
	// Integer countByAgenceCodeAndHandicapeAndRadie(String code,boolean
	// handicape,boolean radie);
	Integer countByAgenceCodeAndNatureAndRadie(String code, ENatureEmploye nature, boolean radie);

	Integer countByAgenceCodeAndRadie(String code, boolean radie);

	Integer countByFonctionCodeAndSexeAndRadie(String code, ESexe sexe, boolean radie);

	// Integer countByFonctionCodeAndTypeContratAndRadie(String code,EtypeContrat
	// contrat,boolean radie);
	// Integer countByFonctionCodeAndHandicapeAndRadie(String code,boolean
	// handicape,boolean radie);
	Integer countByFonctionCodeAndNatureAndRadie(String code, ENatureEmploye nature, boolean radie);

	Integer countByFonctionCodeAndRadie(String code, boolean radie);

	Integer countByClasseCodeAndSexeAndRadie(String code, ESexe sexe, boolean radie);

	// Integer countByClasseCodeAndTypeContratAndRadie(String code,EtypeContrat
	// contrat,boolean radie);
	// Integer countByClasseCodeAndHandicapeAndRadie(String code,boolean
	// handicape,boolean radie);
	Integer countByClasseCodeAndNatureAndRadie(String code, ENatureEmploye nature, boolean radie);

	Integer countByClasseCodeAndRadie(String code, boolean radie);

	@Query(value = "Select e.user From Employe e Where e.matricule = :matricule")
	User findUserByMatricule(@Param("matricule") String matricule);

	long countBySexeAndRadie(ESexe sexe, boolean radie);

	long countByRadie(boolean radie);

	long countByDateAfterAndDateBeforeAndRadie(Date date1, Date date2, boolean radie);

	long countByDateEmbaucheAfterAndDateEmbaucheBeforeAndRadie(Date date1, Date date2, boolean radie);

	long countByDateConfirmationAfterAndDateConfirmationBeforeAndRadie(Date date1, Date date2, boolean radie);

	long countByClasseAndRadie(Classe classe, boolean radie);

	@Query(value = "SELECT SUM(e.salaireBrut) FROM Employe e WHERE e.classe = :classe and e.radie = :radie")
	Double sumSalaryByClasse(@Param("classe") Classe classe, @Param("radie") boolean radie);

	List<Employe> findByRadie(boolean radie);

	@Query(value = "SELECT e FROM Employe e WHERE e.radie = :radie")
	List<Employe> findEmployeRadie(@Param("radie") boolean radie);

	@Query(value = "SELECT e FROM Employe e WHERE e.radie = :radie ORDER BY e.id")
	List<Employe> findAllWithOffsetAndLimit(@Param("radie") boolean radie, Pageable pageable);

	List<Employe> findByAgence_CodeAndRadie(String code, boolean radie);

	long countByAgenceIdAndRadie(Long idNoeudAgence, boolean radie);

	List<Employe> findByFonctionAndRadie(Fonction fonction, boolean radie);

	@Query(value = "Select MAX(e.date) From Employe e Where e.radie = :radie")
	Date getMinDateNaissance(@Param("radie") boolean radie);

	@Query(value = "Select MIN(e.date) From Employe e Where e.radie = :radie")
	Date getMaxDateNaissance(@Param("radie") boolean radie);

	@Query(value = "Select MAX(e.dateEmbauche) From Employe e Where e.radie = :radie")
	Date getMinDateEmbauche(@Param("radie") boolean radie);

	@Query(value = "Select MIN(e.dateEmbauche) From Employe e Where e.radie = :radie")
	Date getMaxDateEmbauche(@Param("radie") boolean radie);

	@Query(value = "Select DISTINCT n.recepteur From NoteDisciplinaire n Where n.statut = :statut and n.emetteur = :manager")
	List<Employe> findEmployeWithNoteDisciplinaire(@Param("statut") ENoteDisciplineStatus statut,
			@Param("manager") Employe e);

	List<Employe> findByAgenceAndRadie(Reseau agence, boolean radie, Pageable pageable);

	long countByAgenceAndRadie(Reseau agence, boolean radie);

	@Query(value = "Select e.matricule From Employe e Where e.radie = :radie AND e.matricule NOT IN :matricules")
	List<String> getMatriculeNotIn(@Param("matricules") Collection<String> matricules, @Param("radie") boolean radie);

	@Query(value = "Select e From Employe e Where  e.matricule IN :matricules")
	Set<Employe> getEmployeMatriculetIn(@Param("matricules") Collection<String> matricules);

	List<Employe> findByUserProfileType(String type);

	List<Employe> findByAgenceCodeInAndRadie(List<String> agences, boolean radie);

	List<Employe> findByFonction(Fonction fonction);

	// Consolidated dashboard statistics query
	@Query("SELECT " +
			"COUNT(e) as totalEmployees, " +
			"COUNT(CASE WHEN e.sexe = org.pkfrc.ehr.entities.utils.enums.ESexe.MASCULIN THEN 1 END) as totalMales, " +
			"COUNT(CASE WHEN e.sexe = org.pkfrc.ehr.entities.utils.enums.ESexe.FEMININ THEN 1 END) as totalFemales, " +
			"AVG(EXTRACT(YEAR FROM CURRENT_DATE) - EXTRACT(YEAR FROM e.date)) as averageAge, " +
			"AVG(EXTRACT(YEAR FROM CURRENT_DATE) - EXTRACT(YEAR FROM e.dateEmbauche)) as averageExperience " +
			"FROM Employe e WHERE e.radie = :radie")
	List<Object[]> getDashboardEmployeeStatistics(@Param("radie") boolean radie);

	// Optimized age range statistics
	@Query("SELECT " +
			"FLOOR((EXTRACT(YEAR FROM CURRENT_DATE) - EXTRACT(YEAR FROM e.date)) / :step) * :step as ageRange, " +
			"COUNT(e) as count " +
			"FROM Employe e WHERE e.radie = :radie AND e.date IS NOT NULL " +
			"GROUP BY FLOOR((EXTRACT(YEAR FROM CURRENT_DATE) - EXTRACT(YEAR FROM e.date)) / :step)")
	List<Object[]> getAgeRangeStatistics(@Param("radie") boolean radie, @Param("step") int step);

	// Optimized experience range statistics
	@Query("SELECT " +
			"FLOOR((EXTRACT(YEAR FROM CURRENT_DATE) - EXTRACT(YEAR FROM e.dateEmbauche)) / :step) * :step as expRange, "
			+
			"COUNT(e) as count " +
			"FROM Employe e WHERE e.radie = :radie AND e.dateEmbauche IS NOT NULL " +
			"GROUP BY FLOOR((EXTRACT(YEAR FROM CURRENT_DATE) - EXTRACT(YEAR FROM e.dateEmbauche)) / :step)")
	List<Object[]> getExperienceRangeStatistics(@Param("radie") boolean radie, @Param("step") int step);

	// Agency-wise employee count in single query
	@Query("SELECT e.agence.id, e.agence.intitule, COUNT(e) " +
			"FROM Employe e WHERE e.radie = :radie " +
			"GROUP BY e.agence.id, e.agence.intitule")
	List<Object[]> getEmployeeCountByAgency(@Param("radie") boolean radie);

}
