<!DOCTYPE html>
<html lang="en" ng-app="App">
  <head>
    <!-- <meta http-equiv="Content-Type" content="text/html; charset=utf-8"> -->
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport"
          content="width=device-width, initial-scale=1, shrink-to-fit=no"/>
    <meta name="description" content="EHR - E Human Resources" />
    <meta name="author" content="PK FOKAM RESEARCH CENTER" />
    <meta name="keyword" content="E Human, Resources" />
    <link rel="shortcut icon" href="img/favicon.png">
    <link rel="stylesheet" href="css/organigram/jquery.jOrgChart.css" />
    <link href="css/organigram/prettify.css" type="text/css" rel="stylesheet" />

    <title>EHR | E Human Resources</title>
    <!-- Main styles for this application -->
    <link href="css/bootstrap.min.css?v=1.0.37" rel="stylesheet" />
    <link href="css/style-customized.css?v=1.0.37" rel="stylesheet" />
    <link href="css/style.css?v=1.0.37" rel="stylesheet" />
    <link href="css/icon/style.css?v=1.0.37" rel="stylesheet" />
    <link href="css/loading-bar.min.css?v=1.0.37" rel="stylesheet" />
    <link href="css/textAngular.css?v=1.0.37" rel="stylesheet" />
    <link href="css/datatable.css?v=1.0.37" rel="stylesheet" />
    <link href="css/angular-confirm.min.css?v=1.0.37" rel="stylesheet" />
    <link href="css/select.min.css?v=1.0.37" rel="stylesheet" />
    <link href="css/app-style.css?v=1.0.37" rel="stylesheet" />
    <link href="css/treeGrid.css?v=1.0.37" rel="stylesheet" />
    <link href="css/ng-weekly-scheduler.css?v=1.0.37" rel="stylesheet" />
    <link href="css/panel.css?v=1.0.37" rel="stylesheet" />

    <link href="css/selectize.default.css?v=1.0.37" rel="stylesheet" />
    <!-- <link rel="stylesheet" -->
    <!-- 	href="https://cdnjs.cloudflare.com/ajax/libs/selectize.js/0.8.5/css/selectize.default.css"> -->

    <!-- <link href="css/flag-icon-css/css/flag-icon.min.css" rel="stylesheet"> -->
    <!-- <link href="css/font-awesome/css/font-awesome.min.css" rel="stylesheet"> -->
    <!-- <link href="css/simple-line-icons/css/simple-line-icons.css" -->
    <!-- 	rel="stylesheet"> -->
    <link href="css/organigram/abn_tree.css?v=1.0.37" type="text/css" rel="stylesheet" />
    <script src="js/vendor.js?v=1.0.37"></script>
    <script src="js/app.js?v=1.0.37"></script>
    <script src="flexmonster/flexmonster.js"></script>
 <!--    <script src="https://cdn.jsdelivr.net/npm/flatted"></script> -->
    <script src="js/lib/flatted.min.js"></script>

<!--    <script src="node_modules/jwt-decode/build/jwt-decode.min.js"></script>-->
<!--    <script src="https://cdn.jsdelivr.net/npm/jwt-decode/build/jwt-decode.min.js"></script>-->
   <!--  <script src="https://cdn.tiny.cloud/1/rp6wmj4rzrqjp0otx3qgn2ya9yk2i521sjwmhqpexrbi8bl4/tinymce/6/tinymce.min.js"></script> -->
    <script src="js/lib/tinymce/tinymce.min.js"></script>


    <!-- <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300&display=swap" rel="stylesheet">
    <link href='https://fonts.googleapis.com/css?family=Inter' rel='stylesheet'> -->

    <!-- Chart links-->
    <!-- <script src="https://unpkg.com/@adminkit/core@latest/dist/js/app.js"></script> -->
    <!--CHart link end -->
  <!--   <script src="https://cdn.flexmonster.com/flexmonster.js"></script> -->
    <style type="text/css">
      @font-face {
  font-family: 'Lato';
  src: url('../main/resources/static/font/lato-v24-latin-regular.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
}
 @font-face {
  font-family: 'Nato Sans JP';
  src: url('../main/resources/static/font/noto-sans-jp-v54-latin-regular.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
}
      .switch {
        position: relative;
        display: inline-block;
        width: 80px;
        height: 24px;
      }

      .switch input {
        display: none;
      }

      .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ff6768;
        -webkit-transition: 0.4s;
        transition: 0.4s;
        -webkit-box-shadow: -4px 3px 4px 2px rgba(140, 58, 70, 1);
        -moz-box-shadow: -4px 3px 4px -2px rgba(140, 58, 70, 1);
        box-shadow: -4px 3px 4px -2px rgba(140, 58, 70, 1);
      }

      .slider:before {
        position: absolute;
        content: "";
        height: 16px;
        width: 16px;
        left: 4px;
        bottom: 4px;
        background-color: white;
        -webkit-transition: 0.4s;
        transition: 0.4s;
        -webkit-box-shadow: -5px -2px 4px -6px rgba(140, 58, 70, 1);
        -moz-box-shadow: -5px -2px 4px -6px rgba(140, 58, 70, 1);
        box-shadow: -5px -2px 4px -6px rgba(140, 58, 70, 1);
      }

      input:checked + .slider {
        background-color: #1b7fbd;
      }

      input:focus + .slider {
        box-shadow: 0 0 1px #2196f3;
      }

      input:checked + .slider:before {
        -webkit-transform: translateX(55px);
        -ms-transform: translateX(55px);
        transform: translateX(55px);
      }

      /*------ ADDED CSS ---------*/
      .on {
        display: none;
      }

      .on,
      .off {
        color: white;
        position: absolute;
        transform: translate(-50%, -50%);
        top: 50%;
        left: 50%;
        font-size: 10px;
        font-family: Verdana, sans-serif;
      }

      input:checked + .slider .on {
        display: block;
      }

      input:checked + .slider .off {
        display: none;
      }

      /*--------- END --------*/

      /* Rounded sliders */
      .slider.round {
        border-radius: 34px;
      }

      .slider.round:before {
        border-radius: 50%;
      }
    </style>
  </head>

  <!-- BODY options, add following classes to body to change options

  // Header options
  1. '.header-fixed'					- Fixed Header

  // Sidebar options
  1. '.sidebar-fixed'					- Fixed Sidebar
  2. '.sidebar-hidden'				- Hidden Sidebar
  3. '.sidebar-off-canvas'		- Off Canvas Sidebar
  4. '.sidebar-minimized'			- Minimized Sidebar (Only icons)
  5. '.sidebar-compact'			  - Compact Sidebar

  // Aside options
  1. '.aside-menu-fixed'			- Fixed Aside Menu
  2. '.aside-menu-hidden'			- Hidden Aside Menu
  3. '.aside-menu-off-canvas'	- Off Canvas Aside Menu

  // Breadcrumb options
  1. '.breadcrumb-fixed'			- Fixed Breadcrumb

  // Footer options
  1. 'footer-fixed'						- Fixed footer

-->

  <body
    class="
      header-fixed
      sidebar-fixed
      aside-menu-fixed aside-menu-hidden
      footer-fixed
    "
  >
    <!-- User Interface -->
    <ui-view class="app"></ui-view>
  </body>

</html>
