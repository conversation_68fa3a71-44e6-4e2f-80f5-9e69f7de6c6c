
package org.pkfrc.ehr.service.paie;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import javax.transaction.Transactional;

import org.apache.log4j.Logger;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.pkfrc.core.entities.security.User;
import org.pkfrc.core.entities.utils.Contact;
import org.pkfrc.core.service.BaseServiceImpl;
import org.pkfrc.core.utilities.enumerations.EOperation;
import org.pkfrc.core.utilities.helper.DateHelper;
import org.pkfrc.core.utilities.result.Result;
import org.pkfrc.core.utilities.result.ValidatorResult;
import org.pkfrc.ehr.entities.admin.ParamCode;
import org.pkfrc.ehr.entities.admin.ParamNotification;
import org.pkfrc.ehr.entities.admin.ParamNotificationRepository;
import org.pkfrc.ehr.entities.admin.Parametre;
import org.pkfrc.ehr.entities.evaluation.document.Document;
import org.pkfrc.ehr.entities.evaluation.document.EEtatDocument;
import org.pkfrc.ehr.entities.evaluation.document.ETypeDocument;
import org.pkfrc.ehr.entities.paie.EEtatPret;
import org.pkfrc.ehr.entities.paie.PeriodePaie;
import org.pkfrc.ehr.entities.paie.Pret;
import org.pkfrc.ehr.entities.paie.payslip.Payslip;
import org.pkfrc.ehr.entities.params.generaux.Exercice;
import org.pkfrc.ehr.entities.params.generaux.employe.Employe;
import org.pkfrc.ehr.entities.utils.Fichier;
import org.pkfrc.ehr.repo.evaluation.ParametreRepository;
import org.pkfrc.ehr.repo.paie.PeriodePaieRepository;
import org.pkfrc.ehr.repo.paie.PretRepository;
import org.pkfrc.ehr.repo.paie.payslip.PayslipRepository;
import org.pkfrc.ehr.repo.params.generaux.employe.EmployeRepository;
import org.pkfrc.ehr.service.evaluation.document.ExportResult;
import org.pkfrc.ehr.service.evaluation.document.IDocumentService;
import org.pkfrc.ehr.service.notification.IMailService;
import org.pkfrc.ehr.service.notification.ISmsService;
import org.pkfrc.ehr.service.params.generaux.IExerciceService;
import org.pkfrc.ehr.service.utils.IFichierService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import net.sf.jasperreports.engine.JRException;

@Service
public class PretServiceImpl extends BaseServiceImpl<Long, Pret> implements IPretService {

	Logger logger = Logger.getLogger(PretServiceImpl.class);

	@Override
	protected Logger getLogger() {
		return logger;
	}

	@Autowired
	IFichierService fichierService;

	@Autowired
	EmployeRepository employeRepo;

	@Autowired
	IDocumentService<Document> docService;

	@Autowired
	IExerciceService exerciceService;

	@Autowired
	ParamNotificationRepository paramNotificationRepository;

	@Autowired
	ISmsService smsService;

	@Autowired
	IMailService mailService;

	@Autowired
	PayslipRepository paySlipRepo;

	@Autowired
	PeriodePaieRepository periodePaieRepo;

	@Autowired
	ParametreRepository parametreRepository;

	@Autowired
	PretRepository pretRepo;

	@Override
	@Transactional
	public Result<Pret> create(User user, Pret record, MultipartFile file, String dir, String subDir, String exercice,
			boolean validate, String lang) throws Exception {

		Result<Pret> result = createResult();
		Employe employe = employeRepo.findByMatricule(record.getEmploye().getMatricule());
		Fichier fichier = new Fichier();
		List<ValidatorResult> validates = new ArrayList<>(0);

		validates = validateEntity(record, EOperation.Save);
		validates.addAll(checkIfPossible(record));

		if (validates.isEmpty()) {
			if (file != null) {
				String directory = dir.substring(dir.indexOf(",") + 1, dir.length());
				String subDirectory = subDir.substring(subDir.indexOf(",") + 1, subDir.length());
				fichier = new Fichier(file.getName(), file.getName(), file.getSize());
				Result<Fichier> upload = fichierService.storeMultipartFile(file, fichier, directory, subDirectory, user,
						exercice, lang);
				record.setDocument(fichier);
				if (!upload.isValid()) {
					validates.add(new ValidatorResult(getClazz().getSimpleName(), "Import",
							"Impossible d'importer le fichier"));
					return super.getInvalidResult(validates, lang);
				}
			}
			if (record.getId() != null) {
				return super.update(user, record, false, lang);
			}
			Long id = pretRepo.getMaxID();
			Date date = new Date();
			Double mensualite = record.getMontant() / record.getNbreEcheance();
			record.setMensualite(mensualite);
			SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
			Long maxCode = (long) (pretRepo.getMaxCode() != null ? pretRepo.getMaxCode() + 1 : 1);
			id = id != null && id > 0L ? id + 1L : 1L;
			record.setCode(maxCode + "");
			record.setLibelle("Demande de pret - " + record.getEmploye().getNoms() + record.getEmploye().getPrenom()
					+ " - " + formatter.format(date) + " - " + record.getTypePret().getIntitule());
			record.setDate(date);
			record.setEtatPret(EEtatPret.EnAttente);
			record.setDateDemandePret(new Date());
			record.setEmploye(employe);
			record.setProprietaire(employe);
			record.setNbreEcheanceRembourse(0);
			record.setType(ETypeDocument.pret);
			record.setEtat(EEtatDocument.VALIDER);
			record.setModifiable(false);
			Result<Exercice> exoRes = exerciceService.checkExercice(record.getExercice(), DEFAULT_LANG);
			if (!exoRes.isValid()) {
				result = super.getInvalidResult(new ArrayList<>(exoRes.getValidators()), lang);
				return result;
			}
			result = super.create(user, record, false, lang);
			if (result.isValid()) {
				ExecutorService executorService = Executors.newSingleThreadExecutor();
				executorService.submit(() -> {
					try {
						notification(record);
					} catch (Exception e) {
						e.printStackTrace();
					}
				});
				executorService.shutdown();

			}

			// if(result.isValid()) {
			// List<Long> ids = new ArrayList<>();
			// ids.add(record.getId());
			// return super.validationManager(user, ids, exercice, null, lang);
			// }else {
			// validates.add(new ValidatorResult(getClazz().getSimpleName(), "Create.fail",
			// result.getMessage()));
			// return super.getInvalidResult(validates, lang);
			// }
			// }

			// return super.getInvalidResult(validates, lang);
		} else {
			return super.getInvalidResult(validates, lang);
		}
		return result;
	}

	public void notification(Pret pret) {
		ParamNotification param = paramNotificationRepository.getAlertDemandePretTemplate();
		Set<Contact> contacts = new HashSet<Contact>();
		contacts.addAll(pret.getEmploye().getContacts());

		HashMap<String, Object> mapper = new HashMap<String, Object>();
		mapper.put("@nom", pret.getEmploye().getNoms() + " " + pret.getEmploye().getPrenom());
		mapper.put("@type", pret.getTypePret().getIntitule());
		mapper.put("@montant", pret.getMontant());

		if (!contacts.isEmpty()) {
			try {
				smsService.sendSms(contacts, "DEMANDE DE PRET", mapper, param);
				mailService.sendMail(contacts, mapper, param);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}

	@Override
	public List<ValidatorResult> checkIfPossible(Pret record) {
		List<ValidatorResult> result = new ArrayList<>(0);
		Long periodeId = periodePaieRepo.getEcours();
		PeriodePaie periode = periodePaieRepo.findById(periodeId);
		PeriodePaie precedentePeriode = periodePaieRepo.findById(periode.getPrecedentePeriode());
		Payslip payslip = paySlipRepo.findByMatriculeAndPeriode(record.getEmploye().getMatricule(), precedentePeriode);
		if (payslip == null) {
			result.add(new ValidatorResult(getClazz().getSimpleName(), "SeuilMontant",
					"Vous n'avez pas encore reçu de paie via EHR !"));
			return result;
		}
		Parametre param = parametreRepository.findByCode(ParamCode.PourcentageDemandePret);

		Double pourcentage = Double.valueOf(param.getField1());
		List<Pret> pretList = pretRepo.getPretEncours(record.getEmploye().getMatricule());
		Double sommeTotal = 0.0;
		if (!pretList.isEmpty()) {
			for (Pret pret : pretList) {
				sommeTotal = sommeTotal + pret.getMensualite();
			}
			sommeTotal += (record.getMontant() / record.getNbreEcheance());
		} else {
			sommeTotal = sommeTotal + (record.getMontant() / record.getNbreEcheance());
		}

		if (sommeTotal > (payslip.getNetSalary() * (pourcentage / 100))) {
			result.add(new ValidatorResult(getClazz().getSimpleName(), "SeuilMontant",
					"Montant élévé, vous avez atteint votre quota mensuel !"));
		}
		return result;
	}

	@Override
	public ExportResult exportPretList(Long[] idList)
			throws JRException, IOException {
		Pret pret;
		String filename = "";

		Workbook wb = new XSSFWorkbook();

		Sheet sheet = wb.createSheet("PRET");

		// Create a Font for styling header cells
		Font headerFont = wb.createFont();
		// headerFont.setBold(true);
		headerFont.setFontHeightInPoints((short) 14);
		headerFont.setColor(IndexedColors.DARK_GREEN.getIndex());

		// Create a CellStyle with the font
		CellStyle headerCellStyle = wb.createCellStyle();
		headerCellStyle.setFont(headerFont);

		// Create a Row
		Row headerRow = sheet.createRow(0);

		SimpleDateFormat formatter = new SimpleDateFormat("dd/MM/YYYY");
		String[] columns = { "CODE_PRET", "MATRICULE", "NOM_EMPLOYE", "TYPE_PRET", "MONTANT", "MENSUALITE", "ETAT_PRET",
				"NOMBRE ECHEANCE REMBOURSE", "RESTE", "DATE DEMANDE PRET", "DATE VALIDATION PRET" };
		for (int i = 0; i < columns.length; i++) {
			Cell cell = headerRow.createCell(i);
			cell.setCellValue(columns[i]);
			cell.setCellStyle(headerCellStyle);
		}
		int rowNum = 1;

		for (Long id : idList) {
			pret = pretRepo.findOne(id);
			Row row = sheet.createRow(rowNum++);
			row.createCell(0).setCellValue(pret.getCode());
			row.createCell(1).setCellValue(pret.getEmploye().getMatricule());
			row.createCell(2).setCellValue(pret.getEmploye().getNoms() + " " + pret.getEmploye().getPrenom());
			row.createCell(3).setCellValue(pret.getTypePret().getIntitule());
			row.createCell(4).setCellValue(pret.getMontant());
			row.createCell(5).setCellValue(pret.getMensualite());

			if (pret.getEtatPret() == EEtatPret.EnAttente) {
				row.createCell(6).setCellValue("En Attente");
			} else if (pret.getEtatPret() == EEtatPret.EnCours) {
				row.createCell(6).setCellValue("En Cours");
			} else if (pret.getEtatPret() == EEtatPret.Rejette) {
				row.createCell(6).setCellValue("Rejetté");
			} else if (pret.getEtatPret() == EEtatPret.Rembourse) {
				row.createCell(6).setCellValue("Remboursé");
			} else if (pret.getEtatPret() == EEtatPret.Suspendu) {
				row.createCell(6).setCellValue("Suspendu");
			}
			row.createCell(7).setCellValue(pret.getNbreEcheance());
			row.createCell(8).setCellValue(pret.getReste());
			row.createCell(9).setCellValue(
					pret.getDateDemandePret() != null ? formatter.format(pret.getDateDemandePret()).toString() : "");
			row.createCell(10)
					.setCellValue(pret.getDateValidationPret() != null
							? formatter.format(pret.getDateValidationPret()).toString()
							: "");
			;
		}

		// Resize all PRET to fit the content size
		for (int i = 0; i < columns.length; i++) {
			sheet.autoSizeColumn(i);
		}

		filename = "LISTE DES PRETS - " + Calendar.getInstance().getTime().getTime();

		logger.info("exportExcel - fileService.saveWorkbook(wb, filename)" + filename);

		return fichierService.saveWorkbook(wb, filename);
	}

	@Override
	protected List<ValidatorResult> validateEntity(Pret record, EOperation operation) {
		List<ValidatorResult> result = new ArrayList<>(0);

		if (record.getId() != null) {
			operation = EOperation.Update;
		}

		if (result.isEmpty() && operation == EOperation.Save) {

			List<Pret> data = pretRepo.findByEmployeMatriculeAndTypePretId(record.getEmploye().getMatricule(),
					record.getTypePret().getId());
			if (!data.isEmpty()) {
				// for(Pret pret : data) {
				// if (pret.getEtatPret() == EEtatPret.EnAttente) {
				// result.add(new ValidatorResult(getClazz().getSimpleName(), "Duplique",
				// "Pret redondant!!!..Un pret de ce type est déjà en attente !"));
				// break;
				// }
				//
				// }

			}
		}
		return result;
	}

	@Override
	public Result<Pret> delete(User user, Long recordId, String lang) throws Exception {
		Result<Pret> result = createResult();

		Pret pret = pretRepo.findById(recordId);
		if (pret == null) {
			result.setValid(false);
			result.setShow(true);
			result.setMessage("Record.not.exists");
			return result;
		}

		Long periodeId = periodePaieRepo.getEcours();
		PeriodePaie periode = periodePaieRepo.findById(periodeId);

		if (pret.getDateValidationPret() != null
				&& DateHelper.before(pret.getDateValidationPret(), periode.getDateDebut())) {
			result.setValid(false);
			result.setShow(true);
			result.setMessage("Item.cannot.be.deleted");
			return result;
		} else if (pret.getDateDemandePret() != null
				&& DateHelper.before(pret.getDateDemandePret(), periode.getDateDebut())) {
			result.setValid(false);
			result.setShow(true);
			result.setMessage("Item.cannot.be.deleted");
			return result;
		}
		return super.delete(user, recordId, lang);
	}

	@Override
	public synchronized List<Pret> getPretEncours(User user, String matricule) {
		return pretRepo.getPretEncours(matricule);
	}

	@Override
	public Class<Pret> getClazz() {
		return Pret.class;
	}

	@Override
	protected List<ValidatorResult> validateAllEntities(Set<Pret> record, EOperation operation) {

		return new ArrayList<>(0);
	}

	// Helper method to convert List<Object[]> to Map<String, Object>
	private Map<String, Object> convertToMap(List<Object[]> resultList) {
		Map<String, Object> resultMap = new HashMap<>();
		if (!resultList.isEmpty() && resultList.get(0).length >= 2) {
			resultMap.put("montantTotal", resultList.get(0)[0]);
			resultMap.put("nombreTotal", resultList.get(0)[1]);
		}
		return resultMap;
	}

	@Override
	public Map<String, Object> getMontantEtNombreTotalPret(String matricule) {
		List<Object[]> result = pretRepo.getMontantEtNombreTotalPret(matricule);
		return convertToMap(result);
	}

	@Override
	public Map<String, Object> getMontantEtNombreTotalPretEncours(String matricule) {
		List<Object[]> result = pretRepo.getMontantEtNombreTotalPretEncours(matricule);
		return convertToMap(result);
	}

	/*
	 * @Override
	 * public Map<String, Object> getMontantEtNombreTotalPretRembourse(String
	 * matricule) {
	 * List<Object[]> result =
	 * pretRepo.getMontantEtNombreTotalPretRembourse(matricule);
	 * return convertToMap(result);
	 * }
	 * 
	 * @Override
	 * public Map<String, Object> getMontantEtNombreTotalPretSuspendu(String
	 * matricule) {
	 * List<Object[]> result =
	 * pretRepo.getMontantEtNombreTotalPretSuspendu(matricule);
	 * return convertToMap(result);
	 * }
	 */

	@Override
	public Map<String, Object> getMontantEtNombreTotalPretRestantEncours(String matricule) {
		List<Object[]> result = pretRepo.getMontantEtNombreTotalPretRestantEncours(matricule);
		return convertToMap(result);
	}

	@Override
	public Map<String, Object> getGLobalPretByState(String matricule, EEtatPret etatPret) {
		List<Object[]> result = pretRepo.getMontantEtNombreTotalGlobalPretEncoursByState(matricule, etatPret);
		return convertToMap(result);
	}

	@Override
	public Map<String, Object> getAllPretStatisticsConsolidated(String matricule) {
		List<Object[]> result = pretRepo.getAllPretStatistics(matricule);
		if (result.isEmpty()) {
			return new HashMap<>();
		}

		Object[] row = result.get(0);
		Map<String, Object> statistics = new HashMap<>();

		// Map the consolidated results
		statistics.put("montantEncours", row[0]);
		statistics.put("nombreEncours", row[1]);
		statistics.put("montantRembourse", row[2]);
		statistics.put("nombreRembourse", row[3]);
		statistics.put("montantSuspendu", row[4]);
		statistics.put("nombreSuspendu", row[5]);
		statistics.put("montantTotal", row[6]);
		statistics.put("nombreTotal", row[7]);

		return statistics;
	}
}
