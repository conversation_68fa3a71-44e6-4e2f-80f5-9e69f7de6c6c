'use strict'

var rev = require('gulp-rev');
var revReplace = require('gulp-rev-replace');

var gulp = require('gulp');
var uglify = require('gulp-uglify');
var minifyHtml = require("gulp-minify-html");
var minifyCss = require("gulp-minify-css");
var jsonminify = require('gulp-jsonminify');
var concat = require('gulp-concat');
var sass = require('gulp-ruby-sass');
// var jshint = require("gulp-jshint");
var header = require("gulp-header");
var del = require('del');
var fs = require('fs')
var runSequence = require('run-sequence');
var order = require("gulp-order");
var pump = require('pump');

gulp.paths = {
	dist : 'dist/',
	src : 'src/app/',
	vendors : 'dist/vendors/'
};

var buildDir = 'src/main/resources/static/minjs';

var localCI = 'fr-ci';
var localSTP = 'pt-st';
var localCM = 'fr';
var localCMA = 'fr-cm';
var locale = 'node_modules/angular-i18n/angular-locale_' + localCMA + '.js'
var localeM = 'node_modules/moments/locale/' + localCM + '.js'
var vendorJS = [ 'node_modules/a_libs/jquery.min.js',
		'node_modules/a_libs/jquery.validate.js', 'node_modules/a_libs/popper.min.js',
		'node_modules/a_libs/bootstrap.min.js', 'node_modules/a_libs/angular.min.js',
		'node_modules/a_libs/angular-animate.min.js',
		'node_modules/a_libs/angular-sanitize.min.js',
		'node_modules/a_libs/angular-ui-router.min.js',
		'node_modules/a_libs/ocLazyLoad.min.js',
		'node_modules/a_libs/angular-breadcrumb.min.js',

		'node_modules/a_libs/angular-confirm.min.js',
		'node_modules/a_libs/angular-cookies.min.js',
		'node_modules/a_libs/angular-resource.js',
		'node_modules/a_libs/angular-file-saver.bundle.min.js',
		'node_modules/a_libs/angular-messages.min.js',

		'node_modules/a_libs/datatable/pagination.min.js',
		'node_modules/a_libs/datatable/dataGridUtils.min.js',
		'node_modules/a_libs/datatable/dataGrid.min.js',
		'node_modules/a_libs/angular-bootstrap-checkbox.js',
		'node_modules/a_libs/select.min.js',

		'node_modules/a_libs/textAngular/textAngularSetup.js',
		'node_modules/a_libs/textAngular/textAngular-rangy.min.js',
		'node_modules/a_libs/textAngular/textAngular-sanitize.min.js',
		'node_modules/a_libs/textAngular/textAngular.min.js',

		'node_modules/a_libs/translate/angular-translate.min.js',
		'node_modules/a_libs/translate/angular-translate-storage-cookie.min.js',
		'node_modules/a_libs/translate/angular-translate-storage-local.min.js',
		'node_modules/a_libs/translate/angular-translate-loader-partial.min.js',

		'node_modules/a_libs/loading-bar.min.js', 'node_modules/a_libs/Chart.min.js',
		'node_modules/a_libs/angular-chart.js',

		'node_modules/a_libs/dependencies/jquery.jOrgChart.js',
		'node_modules/a_libs/dependencies/jquery.mousewheel.js',
		'node_modules/a_libs/dependencies/jquery.panzoom.js',
		'node_modules/a_libs/dependencies/prettify.js',
		'node_modules/a_libs/dependencies/ui-bootstrap-tpls-2.5.0.min.js',
		
		'node_modules/a_libs/chart/echarts.js',
		'node_modules/a_libs/chart/chart.js',
		
		
		'node_modules/a_libs/calendrier/moment.js',
		'node_modules/a_libs/calendrier/tmhDynamicLocale.js',
		'node_modules/a_libs/calendrier/ng-weekly-scheduler.js'
		];
// ,
// 'node_modules/sanitize.min.js',
// var appJS = [ 'js/general/app.js', 'js/general/modules.js',
// 'js/general/config.js', 'js/utils/*', 'js/services/**',
// 'js/directives/**', 'js/template/controllers/**',
// 'js/authentication/**' ];
var appJS = [ gulp.paths.src + 'app.js' ];

var modules = [ 'core', 'common', 'security', 'ui_core', 'ams', 'org' ]
for (var i = 0; i < modules.length; i++) {
	appJS.push(gulp.paths.src + modules[i] + '/modules.js');
	appJS.push(gulp.paths.src + modules[i] + '/**/*.js');
}
appJS.push(gulp.paths.src + 'modules.js');
appJS.push(gulp.paths.src + 'config.js');
appJS.push(gulp.paths.src + 'run.js');
// console.log(appJS);

gulp.task('clean:dist', function() {
	return del(gulp.paths.dist);
});
gulp.task('copy:json', function() {
	return gulp.src(gulp.paths.src + '/i18n/*.json').pipe(jsonminify())
	// .pipe(jsonminify.reporter('fail'))
	.pipe(gulp.dest(gulp.paths.dist + 'i18n'));
});
gulp.task('copy:js', function() {
	return gulp.src(appJS)
	// .pipe(jshint())
	// .pipe(jshint.reporter('fail'))
		.pipe(concat('app.js'))
		
		//.pipe(rev())
		//.pipe(rev.manifest())

		// .pipe(uglify({
	// compress : {
	// hoist_funs : false
	// }
	// }))
	.pipe(gulp.dest(gulp.paths.dist + 'js'));
});

/*gulp.task('copy:js', function(cb) {
	pump([
		gulp.src(appJS),
		concat('app.js'),
		uglify({
			compress: {
				drop_console: true,
				drop_debugger: true
			}
		}),
		gulp.dest(gulp.paths.dist + 'js')
	], cb);
});*/

gulp.task('copy_vendor:js', function() {
	return gulp.src(vendorJS)
	// .pipe(jshint())
	// .pipe(jshint.reporter('fail'))
		.pipe(concat('vendor.js'))
		//.pipe(uglify())
		// .pipe(rev())
		// .pipe(rev.manifest())
	// .pipe(uglify({
	// compress : {
	// hoist_funs : false
	// }
	// }))
	.pipe(gulp.dest('src/main/resources/static/' + 'js'));
});

gulp.task('copy:pages', function() {
	return gulp.src(
			[ '!' + gulp.paths.src + 'index.html',
					gulp.paths.src + '/**/*.html' ]).pipe(minifyHtml()).pipe(
			gulp.dest(gulp.paths.dist + 'pages'));
});
gulp.task('copy:index', function() {
	return gulp.src(gulp.paths.src + 'index.html').pipe(
			gulp.dest(gulp.paths.dist));
});

gulp.task('copy:app', function() {
	return gulp.src(gulp.paths.dist + '**/*').pipe(
			gulp.dest('src/main/resources/static'));
});

gulp.task('build:app',
		function(callback) {
			runSequence('clean:dist', 'copy:js', 'copy:pages', 'copy:json',
					'copy:index', 'copy_vendor:js', 'copy:app', 'clean:dist',
					callback);
		});
gulp.task('build:app:lite', function(callback) {
	runSequence('clean:dist', 'copy:js', 'copy:pages', 'copy:json',
			'copy:index', 'copy:app', 'clean:dist', callback);
});

// Watches spécifiques pour chaque type de fichier
gulp.task('watch:js', function() {
	gulp.watch(gulp.paths.src + '**/*.js', ['copy:js', 'copy:app']);
});

gulp.task('watch:html', function() {
	gulp.watch(gulp.paths.src + '**/*.html', ['copy:pages', 'copy:index', 'copy:app']);
});

gulp.task('watch:json', function() {
	gulp.watch(gulp.paths.src + '/i18n/*.json', ['copy:json', 'copy:app']);
});

// Tâche watch principale qui lance toutes les watches spécifiques
gulp.task('watch', ['watch:js', 'watch:html', 'watch:json']);

gulp.task('default', function(callback) {
	runSequence('build:app', 'watch', callback);
});
