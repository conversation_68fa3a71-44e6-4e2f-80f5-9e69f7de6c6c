{"_from": "pump", "_id": "pump@3.0.3", "_inBundle": false, "_integrity": "sha512-todwxLMY7/heScKmntwQG8CXVkWUOdYxIvY2s0VWAAMh/nd8SoYiRaKjlr7+iCs984f2P8zvrfWcDDYVb73NfA==", "_location": "/pump", "_phantomChildren": {"once": "1.4.0"}, "_requested": {"type": "tag", "registry": true, "raw": "pump", "name": "pump", "escapedName": "pump", "rawSpec": "", "saveSpec": null, "fetchSpec": "latest"}, "_requiredBy": ["#DEV:/", "#USER"], "_resolved": "https://registry.npmjs.org/pump/-/pump-3.0.3.tgz", "_shasum": "151d979f1a29668dc0025ec589a455b53282268d", "_spec": "pump", "_where": "D:\\Dev projets\\PKF_Work\\EHR\\ehr_afb\\ehr-web", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "browser": {"fs": false}, "bugs": {"url": "https://github.com/mafintosh/pump/issues"}, "bundleDependencies": false, "dependencies": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}, "deprecated": false, "description": "pipe streams together and close all of them if one of them closes", "homepage": "https://github.com/mafintosh/pump#readme", "keywords": ["streams", "pipe", "destroy", "callback"], "license": "MIT", "name": "pump", "repository": {"type": "git", "url": "git://github.com/mafintosh/pump.git"}, "scripts": {"test": "node test-browser.js && node test-node.js"}, "version": "3.0.3"}