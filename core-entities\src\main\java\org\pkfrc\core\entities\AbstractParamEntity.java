package org.pkfrc.core.entities;

import java.io.Serializable;

import javax.persistence.MappedSuperclass;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;
import lombok.EqualsAndHashCode;

@MappedSuperclass
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@EqualsAndHashCode(callSuper = true, onlyExplicitlyIncluded = true)
public abstract class AbstractParamEntity<T extends Serializable> extends AbstractEntity<T>
		implements Comparable<AbstractParamEntity<T>> {

	private static final long serialVersionUID = 1L;

	@EqualsAndHashCode.Include
	protected String code;

	protected String intitule;

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getIntitule() {
		return intitule;
	}

	public void setIntitule(String intitule) {
		this.intitule = intitule;
	}

	@Override
	public String toString() {
		return intitule;
	}

	@Override
	public int compareTo(AbstractParamEntity<T> arg0) {
		return getCode().compareToIgnoreCase(arg0.getCode());
	}

}
