/**
 * @license Projurise (c) 2018
 * <AUTHOR>
 */
(function(Controllers, undefined) {

	App.Modules.Core
			.controller(
					"ChangePasswordController",
					[
							"$http",
							"$location",
							"$state",
							'$controller',
							'$filter',
							'authenticationService',
							'base',
							'result',
							'reason',
							'$cookies',
							'$window',
							'$translate',		
							function($http, $location, $state, $controller,
									$filter, authenticationService, base,
									result, reason, $cookies, $window, $translate) {
								if (angular.isUndefined(result)
										|| result == null) {
									$state.go('login');
								}

								var baseCtrl = $controller("BaseController", {});
								angular.extend(this, baseCtrl);

								var vm = this;
								vm.result = result;
								vm.changePwd = changePwd;
								vm.cancel = cancel;
								vm.record = {}
								switch (reason) {
								case "change":
									vm.title = "Change password";
									vm.subTitle = "You are about to change your password";
									break;
								case "default_password":
									vm.title = "Change default password";
									vm.subTitle = "You were assigned a default password, please change it to continue";
									break;
								case "password_expired":
									vm.title = "Password expired";
									vm.subTitle = "Your password has expired, please change it to continue";
									break;
								}
								function changePwd() {

									authenticationService
											.changepwd(result.id,
													vm.record.current,
													vm.record.password,
												vm.record.confirmation)
											.then(
													function(result) {
														if (!result.data.valid) {
															baseCtrl
																	.openErrorDialog(result.data.validators);
														} else {
															treatValidResult(result);
														}
													})
								}
								function cancel() {
									switch (reason) {
									case "change":
										$location.path(App.previous);
										break;
									case "default_password":

										//$state.go('login');
										authenticationService
												.logout()
												.then(
														function(result) {

															$cookies
																	.remove('exercice');
															$cookies
																	.remove('token');

															$state.go('login');
															// $state.go("simple.login");

														});
										break;
									case "password_expired":
										$state.go('login');
										break;
									}
								}

								function treatValidResult(result) {
									vm.confirmationDialog(
											$filter('pkfTranslate')(
													'PASSWORD_CHANGED'),
											success, 'blue');
									if (reason == "change") {
										$state.go('login');
									} else {
										$state.go('login',{
															"record" : {
																id : result.data.id,
																login : result.data.login,
																type : result.data.profile.type,
																disable : true
															}
														});
									}
								}

								vm.togglePassword = togglePassword;
								function togglePassword() {
									const passwordField = document.getElementById('password');
									const icon = document.querySelector('.toggle-password');

									// Toggle between password and text type
									if (passwordField.type === 'password') {
										passwordField.type = 'text';
										icon.textContent = '🙈';  // Change the icon to closed-eye
									} else {
										passwordField.type = 'password';
										icon.textContent = '👁️';  // Change the icon to open-eye
									}
								}

								vm.togglePasswordOld = togglePasswordOld;
								function togglePasswordOld() {
									const passwordField = document.getElementById('current');
									const icon = document.querySelector('.toggle-passwordOld');

									// Toggle between password and text type
									if (passwordField.type === 'password') {
										passwordField.type = 'text';
										icon.textContent = '🙈';  // Change the icon to closed-eye
									} else {
										passwordField.type = 'password';
										icon.textContent = '👁️';  // Change the icon to open-eye
									}
								}

								vm.togglePasswordConfirmation = togglePasswordConfirmation;
								function togglePasswordConfirmation() {
									const passwordField = document.getElementById('confirmation');
									const icon = document.querySelector('.toggle-passwordConf');

									// Toggle between password and text type
									if (passwordField.type === 'password') {
										passwordField.type = 'text';
										icon.textContent = '🙈';  // Change the icon to closed-eye
									} else {
										passwordField.type = 'password';
										icon.textContent = '👁️';  // Change the icon to open-eye
									}
								}

								 // Ajout des méthodes de force du mot de passe (code ci-dessus)
    vm.getPasswordStrengthClass = function() {
        return getPasswordStrengthClass(vm);
    };
    
    vm.getPasswordStrengthText = function() {
        return getPasswordStrengthText(vm);
    };
    
    vm.getPasswordSuggestions = function() {
        return getPasswordSuggestions(vm);
    };
    
    vm.getPasswordMatchClass = function() {
        return getPasswordMatchClass(vm);
    };
    
    vm.getPasswordMatchText = function() {
        return getPasswordMatchText(vm);
    };

	/**
 * Calcule le score de force du mot de passe
 * @param {string} password - Le mot de passe à analyser
 * @returns {number} Score entre 0 et 4
 */
function calculatePasswordStrength(password) {
    if (!password) return 0;
    
    let score = 0;
    
    // Longueur du mot de passe
    if (password.length >= 8) score++;
    if (password.length >= 12) score++;
    
    // Contient des minuscules
    if (/[a-z]/.test(password)) score++;
    
    // Contient des majuscules
    if (/[A-Z]/.test(password)) score++;
    
    // Contient des chiffres
    if (/[0-9]/.test(password)) score++;
    
    // Contient des caractères spéciaux
    if (/[^A-Za-z0-9]/.test(password)) score++;
    
    // Bonus pour la diversité des caractères
    const uniqueChars = new Set(password).size;
    if (uniqueChars >= password.length * 0.7) score++;
    
    // Pénalité pour les patterns répétitifs
    if (/(.)\1{2,}/.test(password)) score--; 
    if (/123|abc|qwe|asd|zxc/i.test(password)) score--;
	if (password.length < 4) score--;
	if (password.length < 3) score--;
	if (password.length < 2) score--;
    
    // Assurer que le score reste dans la plage 0-4
    return Math.max(0, Math.min(4, score));
}

/**
 * Retourne la classe CSS correspondant à la force du mot de passe
 * @param {Object} vm - ViewModel object containing the record
 * @returns {string} Classe CSS ('weak', 'fair', 'good', 'strong')
 */
function getPasswordStrengthClass(vm) {
    if (!vm.record || !vm.record.password) {
        return '';
    }
    
    const strength = calculatePasswordStrength(vm.record.password);
    
    switch (strength) {
        case 0:
        case 1:
            return 'weak';
        case 2:
            return 'fair';
        case 3:
            return 'good';
        case 4:
        default:
            return 'strong';
    }
};

/**
 * Retourne le texte descriptif de la force du mot de passe
 * @param {Object} vm - ViewModel object containing the record
 * @returns {string} Texte descriptif
 */
function getPasswordStrengthText(vm) {
    if (!vm.record || !vm.record.password) {
        return '';
    }
    
    const strength = calculatePasswordStrength(vm.record.password);
    
    switch (strength) {
        case 0:
            return $filter('pkfTranslate')(
							"Mot_de_passe_très_faible");
        case 1:
            return $filter('pkfTranslate')(
							"Mot_de_passe_faible");
        case 2:
            return $filter('pkfTranslate')(
							"Mot_de_passe_moyen");
        case 3:
            return $filter('pkfTranslate')(
							"Mot_de_passe_bon");
        case 4:
        default:
            return $filter('pkfTranslate')(
							"Mot_de_passe_excellent");
    }
};

/**
 * Méthode optionnelle pour obtenir des conseils d'amélioration
 * @param {Object} vm - ViewModel object containing the record
 * @returns {Array} Liste des conseils d'amélioration
 */
function getPasswordSuggestions(vm) {
    if (!vm.record || !vm.record.password) {
        return [];
    }
    
    const password = vm.record.password;
    const suggestions = [];
    
    if (password.length < 8) {
        suggestions.push($filter('pkfTranslate')(
							"Utilisez_au_moins_8_caractères"));
    }
    
    if (!/[a-z]/.test(password)) {
        suggestions.push($filter('pkfTranslate')(
							"Ajoutez_des_lettres_minuscules"));
    }
    
    if (!/[A-Z]/.test(password)) {
        suggestions.push($filter('pkfTranslate')(
							"Ajoutez_des_lettres_majuscules"));
    }
    
    if (!/[0-9]/.test(password)) {
        suggestions.push($filter('pkfTranslate')(
							"Ajoutez_des_chiffres"));
    }
    
    if (!/[^A-Za-z0-9]/.test(password)) {
        suggestions.push($filter('pkfTranslate')(
							"Ajoutez_des_caracteres_speciaux"));
    }
    
    if (/(.)\1{2,}/.test(password)) {
        suggestions.push($filter('pkfTranslate')(
							"Evitez_les_caracteres_repetitifs"));
    }
    
    if (/123|abc|qwe|asd|zxc/i.test(password)) {
        suggestions.push($filter('pkfTranslate')(
							"Evitez_les_sequences_communes"));
    }
    
    return suggestions;
};

/**
 * Méthode pour vérifier si les mots de passe correspondent
 * @param {Object} vm - ViewModel object containing the record
 * @returns {boolean} True si les mots de passe correspondent
 */
function passwordsMatch(vm) {
    if (!vm.record || !vm.record.password || !vm.record.confirmation) {
        return false;
    }
    
    return vm.record.password === vm.record.confirmation;
};

/**
 * Méthode pour obtenir la classe CSS de correspondance des mots de passe
 * @param {Object} vm - ViewModel object containing the record
 * @returns {string} Classe CSS pour la correspondance
 */
function getPasswordMatchClass(vm) {
    if (!vm.record || !vm.record.confirmation) {
        return '';
    }
    
    return passwordsMatch(vm) ? 'match' : 'no-match';
};

/**
 * Méthode pour obtenir le texte de correspondance des mots de passe
 * @param {Object} vm - ViewModel object containing the record
 * @returns {string} Texte de correspondance
 */
function getPasswordMatchText(vm) {
    if (!vm.record || !vm.record.confirmation) {
        return '';
    }
    
    return passwordsMatch(vm) ? $filter('pkfTranslate')(
							"Les_mots_de_passe_correspondent") : $filter('pkfTranslate')(
							"Les_mots_de_passe_ne_correspondent_pas");;
};

} ]);

}(App.Controllers = App.Controllers || {}));
