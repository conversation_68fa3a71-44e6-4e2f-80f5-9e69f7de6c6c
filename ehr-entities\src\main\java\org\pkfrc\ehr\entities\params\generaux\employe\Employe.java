package org.pkfrc.ehr.entities.params.generaux.employe;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Embedded;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.ForeignKey;
import javax.persistence.Inheritance;
import javax.persistence.InheritanceType;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.pkfrc.core.entities.audit.Auditable;
import org.pkfrc.core.entities.security.User;
//import org.pkfrc.ehr.entities.absence.Conge;
//import org.pkfrc.ehr.entities.absence.DemandeConge;
import org.pkfrc.ehr.entities.noeud.Organigramme;
import org.pkfrc.ehr.entities.noeud.Reseau;
import org.pkfrc.ehr.entities.paie.ModePaiement;
import org.pkfrc.ehr.entities.paie.ModeleFichePaie;
import org.pkfrc.ehr.entities.paie.RubriqueEmploye;
import org.pkfrc.ehr.entities.paie.comptabilte.CompteEmploye;
import org.pkfrc.ehr.entities.params.generaux.Fonction;
import org.pkfrc.ehr.entities.params.specifiques.LissageParEmploye;
import org.pkfrc.ehr.entities.utils.Cv;
import org.pkfrc.ehr.entities.utils.Diplome;
import org.pkfrc.ehr.entities.utils.Fichier;
import org.pkfrc.ehr.entities.utils.Personne;

import com.fasterxml.jackson.annotation.JsonIgnore;

/**
 * 
 * Ams (c) 2018 PKFokam Research Center
 * 
 * <AUTHOR> steve - <EMAIL>
 */

@Entity
@Inheritance(strategy = InheritanceType.TABLE_PER_CLASS)
@Table(name = "PKF_EHR_EMPLOYE")
// @EntityListeners(AuditingEntityListener.class)
public class Employe extends Personne implements Comparable<Employe> {

	private static final long serialVersionUID = 1L;

	private Organigramme posteResponsabilite;
	private Fonction fonction;
	private Cv cv;
	private Date dateConfirmation;
	private Date dateEmbauche;
	private Date dateFinEmbauche;
	private String motifRupContrat;
	private Classe classe;
	private Grade grade;
	private Organigramme unite;
	private Reseau agence;
	private Reseau agenceVirement;
	private Organigramme cellule;
	private User user;
	private boolean radie;
	private String matriculeCnps;
	private String numContribuable;
	private String matricule;
	private String numeroCompte;
	private String codeBanqueCompte;
	private String codeAgenceCompte;
	private Double salaireBrut;
	private Double salaireNet;
	private Double salaireBase;
	private String lieuCnps;
	private String employeur;
	private Date dateCnps;
	private Contrat contratEncours;
	private Set<Fichier> documents = new HashSet<>();
	private Set<Enfant> enfants = new HashSet<>();
	private Set<Employe> managers = new HashSet<>();
	private Set<FonctionEmploye> fonctions = new HashSet<>();
	private Set<Diplome> diplomes = new HashSet<>();
	private List<CompteEmploye> comptes = new ArrayList<>();
	private boolean enfantEncharge = Boolean.FALSE;
	private boolean conjointSalarie = Boolean.FALSE;
	private Set<LissageParEmploye> lissage = new HashSet<>();
	private ENatureEmploye nature;
	private byte[] photoByteArray;
	/*
	 * private Set<DemandeConge> dmdConges = new HashSet<DemandeConge>(0); private
	 * Set<Conge> listConge = new HashSet<Conge>(0);
	 */
	/**
	 * liste des rubriques Suplementaire de l employe
	 */
	private Set<RubriqueEmploye> rubriquesEmploye = new HashSet<>();
	private ModeleFichePaie modeleFichePaie;
	private Integer nbJourTravail = 30;
	private Integer annesExperience = 0;
	private Integer nombrePoints = 0;
	/**
	 * Avance sur salaire ou valeur echeance courante d'un prêt obtenu par la banque
	 */
	private Double pretOuAvance = 0d;
	private Integer nombreDomestique = 0;
	private EStatutMatrimonial statutMatrimonial;

	private ModePaiement modePaiement;

	private StatutEmploye statutEmploye;
	private CasierJudiciaire casierJudiciaire;
	private ENationalStatut nationalStatus;
	private String autorisationNumber;
	/**
	 * Donne la faculté d'afficher le taux de matching entre les formations
	 * presentent dans le cv de l'employé et celles requisent par sa fonction
	 */
	private Double percentageMatchingFormations;
	private Boolean premierEmploie = Boolean.FALSE;
	private Date dateInstaurationPremierEmploi;

	private EStatutEmploye statut;
	private String numeroIdentificationUnique;

	@Column(name = "EMP_MATRICULECNPS")
	public String getMatriculeCnps() {
		return matriculeCnps;
	}

	@Column(name = "EMP_NUMEROCOMPTE")
	public String getNumeroCompte() {
		return numeroCompte;
	}

	@Column(name = "EMP_SALAIREBRUT")
	public Double getSalaireBrut() {
		return salaireBrut;
	}

	@Column(name = "EMP_SALAIRENET")
	public Double getSalaireNet() {
		return salaireNet;
	}

	@OneToMany(fetch = FetchType.LAZY, orphanRemoval = true, cascade = CascadeType.ALL)
	@JoinColumn(name = "EMPLOYE_ID", foreignKey = @ForeignKey(name = "FK_DOCUMENT"))
	public Set<Fichier> getDocuments() {
		return documents;
	}

	public void setMatriculeCnps(String matriculeCnps) {
		this.matriculeCnps = matriculeCnps;
	}

	public void setNumeroCompte(String numeroCompte) {
		this.numeroCompte = numeroCompte;
	}

	@Column(name = "EMP_NUM_CONTRIBUABLE")
	public String getNumContribuable() {
		return numContribuable;
	}

	public void setNumContribuable(String numContribuable) {
		this.numContribuable = numContribuable;
	}

	public void setSalaireBrut(Double salaireBrut) {
		this.salaireBrut = salaireBrut;
	}

	public void setSalaireNet(Double salaireNet) {
		this.salaireNet = salaireNet;
	}

	public void setDocuments(Set<Fichier> documents) {
		this.documents = documents;
	}

	@Column(name = "EMP_DTE_CONFIRMATION")
	@Temporal(TemporalType.DATE)
	public Date getDateConfirmation() {
		return dateConfirmation;
	}

	public void setDateConfirmation(Date dateConfirmation) {
		this.dateConfirmation = dateConfirmation;
	}

	@Column(name = "EMP_DTE_EMBAUCHE")
	@Temporal(TemporalType.DATE)
	public Date getDateEmbauche() {
		return dateEmbauche;
	}

	public void setDateEmbauche(Date dateEmbauche) {
		this.dateEmbauche = dateEmbauche;
	}

	@Column(name = "EMP_DTE_FIN_EMBAUCHE")
	@Temporal(TemporalType.DATE)
	public Date getDateFinEmbauche() {
		return dateFinEmbauche;
	}

	public void setDateFinEmbauche(Date dateFinEmbauche) {
		this.dateFinEmbauche = dateFinEmbauche;
	}

	@Column(name = "EMP_MOTIFRUPCONTRAT")
	public String getMotifRupContrat() {
		return motifRupContrat;
	}

	public void setMotifRupContrat(String motifRupContrat) {
		this.motifRupContrat = motifRupContrat;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "CLASSE_ID", foreignKey = @ForeignKey(name = "FK_EMPLOYE_CLASSE"))
	public Classe getClasse() {
		return classe;
	}

	public void setClasse(Classe classe) {
		this.classe = classe;
	}

	@OneToOne(fetch = FetchType.LAZY, orphanRemoval = true, cascade = CascadeType.ALL)
	@JoinColumn(name = "CV_ID", foreignKey = @ForeignKey(name = "FK_EMPLOYE_CV"))
	public Cv getCv() {
		return cv;
	}

	public void setCv(Cv cv) {
		this.cv = cv;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "GRADE_ID", foreignKey = @ForeignKey(name = "FK_EMPLOYE_GRADE"))
	public Grade getGrade() {
		return grade;
	}

	public void setGrade(Grade grade) {
		this.grade = grade;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "UNITE_ID", foreignKey = @ForeignKey(name = "FK_EMPLOYE_UNITE"))
	public Organigramme getUnite() {
		return unite;
	}

	public void setUnite(Organigramme unite) {
		this.unite = unite;
	}

	@ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.REMOVE)
	@JoinColumn(name = "AGENCE_ID", foreignKey = @ForeignKey(name = "FK_EMPLOYE_AGENCE"))
	public Reseau getAgence() {
		return agence;
	}

	public void setAgence(Reseau agence) {
		this.agence = agence;
	}

	@ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.REMOVE)
	@JoinColumn(name = "AGENCE_VIREMENT_ID", foreignKey = @ForeignKey(name = "FK_EMPLOYE_AGENCE_VIREMENT"))
	public Reseau getAgenceVirement() {
		return agenceVirement;
	}

	public void setAgenceVirement(Reseau agenceVirement) {
		this.agenceVirement = agenceVirement;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "CELLULE_ORG_ID", foreignKey = @ForeignKey(name = "FK_EMPLOYE_CELLULE"))
	public Organigramme getCellule() {
		return cellule;
	}

	public void setCellule(Organigramme cellule) {
		this.cellule = cellule;
	}

	@OneToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "USER_ID", foreignKey = @ForeignKey(name = "FK_EMPLOYE_USER"))
	public User getUser() {
		return user;
	}

	public void setUser(User user) {
		this.user = user;
	}

	@Column(name = "EMP_READIE")
	public boolean isRadie() {
		return radie;
	}

	public void setRadie(boolean radie) {
		this.radie = radie;
	}

	@OneToMany(fetch = FetchType.LAZY, orphanRemoval = true, cascade = CascadeType.ALL)
	@JoinColumn(name = "EMP_ID", foreignKey = @ForeignKey(name = "FK_FONCTION_EMPLOYE"))
	public Set<FonctionEmploye> getFonctions() {
		return fonctions;
	}

	public void setFonctions(Set<FonctionEmploye> fonctions) {
		this.fonctions = fonctions;
	}

	@OneToMany(fetch = FetchType.LAZY, orphanRemoval = true, cascade = CascadeType.ALL)
	@JoinColumn(name = "EMPLOYE_ID")
	public List<CompteEmploye> getComptes() {
		return comptes;
	}

	public void setComptes(List<CompteEmploye> comptes) {
		this.comptes = comptes;
	}

	@Column(name = "EMP_LIEU_CNPS")
	public String getLieuCnps() {
		return lieuCnps;
	}

	public void setLieuCnps(String lieuCnps) {
		this.lieuCnps = lieuCnps;
	}

	@Column(name = "EMP_EMPLOYEUR")
	public String getEmployeur() {
		return employeur;
	}

	public void setEmployeur(String employeur) {
		this.employeur = employeur;
	}

	@Column(name = "EMP_DTE_CNPS")
	@Temporal(TemporalType.DATE)
	public Date getDateCnps() {
		return dateCnps;
	}

	@OneToMany(fetch = FetchType.LAZY, orphanRemoval = true, cascade = CascadeType.ALL)
	@JoinColumn(name = "EMPLOYE_ID", foreignKey = @ForeignKey(name = "FK_ENFANTS"))
	public Set<Enfant> getEnfants() {
		return enfants;
	}

	public void setEnfants(Set<Enfant> enfants) {
		this.enfants = enfants;
	}

	public void setDateCnps(Date dateCnps) {
		this.dateCnps = dateCnps;
	}

	@Column(name = "EMP_ENFANTENCHARGE")
	public boolean isEnfantEncharge() {
		return enfantEncharge;
	}

	@Column(name = "EMP_CONJOINTSALARIE")
	public boolean isConjointSalarie() {
		return conjointSalarie;
	}

	public void setEnfantEncharge(boolean enfantEncharge) {
		this.enfantEncharge = enfantEncharge;
	}

	public void setConjointSalarie(boolean conjointSalarie) {
		this.conjointSalarie = conjointSalarie;
	}

	@Column(name = "EMP_MATRICULE")
	public String getMatricule() {
		return matricule;
	}

	public void setMatricule(String matricule) {
		this.matricule = matricule;
	}

	@JsonIgnore
	@ManyToMany(fetch = FetchType.LAZY)
	@JoinTable(name = "PKF_EHR_EMP_MANAGR", joinColumns = { @JoinColumn(name = "EMPLOYE_ID") }, inverseJoinColumns = {
			@JoinColumn(name = "MANAGER_ID", nullable = false) })
	public Set<Employe> getManagers() {
		return managers;
	}

	public void setManagers(Set<Employe> managers) {
		this.managers = managers;
	}

	@OneToMany(fetch = FetchType.LAZY, orphanRemoval = true, cascade = CascadeType.ALL)
	@JoinColumn(name = "ID_EMPLOYE", foreignKey = @ForeignKey(name = "FK_DIPLOME_EMPLOYE"))
	public Set<Diplome> getDiplomes() {
		return diplomes;
	}

	public void setDiplomes(Set<Diplome> diplomes) {
		this.diplomes = diplomes;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "employe", cascade = CascadeType.ALL)
	public Set<LissageParEmploye> getLissage() {
		return lissage;
	}

	public void setLissage(Set<LissageParEmploye> lissage) {
		this.lissage = lissage;
	}

	@Enumerated(EnumType.STRING)
	@Column(name = "NATURE_EMPLOYE")
	public ENatureEmploye getNature() {
		return nature;
	}

	public void setNature(ENatureEmploye nature) {
		this.nature = nature;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "FONCTION_ID", foreignKey = @ForeignKey(name = "FK_FONCTION_ID"))
	public Fonction getFonction() {
		return fonction;
	}

	public void setFonction(Fonction fonction) {
		this.fonction = fonction;
	}

	@OneToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "POSTE_RESPOSABILITE_ID", foreignKey = @ForeignKey(name = "FK_POSTE_RESPOSABILITE_ID"))
	public Organigramme getPosteResponsabilite() {
		return posteResponsabilite;
	}

	public void setPosteResponsabilite(Organigramme posteResponsabilite) {
		this.posteResponsabilite = posteResponsabilite;
	}

	@Column(name = "EMP_SALAIRE_BASE")
	public Double getSalaireBase() {
		return salaireBase;
	}

	public void setSalaireBase(Double salaireBase) {
		this.salaireBase = salaireBase;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "employe")
	public Set<RubriqueEmploye> getRubriquesEmploye() {
		return rubriquesEmploye;
	}

	public void setRubriquesEmploye(Set<RubriqueEmploye> rubriquesEmploye) {
		this.rubriquesEmploye = rubriquesEmploye;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "MODELE_FICHE_PAIE_ID", foreignKey = @ForeignKey(name = "FK_EMPLOYE_PAYMODEL_ID"))
	public ModeleFichePaie getModeleFichePaie() {
		return modeleFichePaie;
	}

	public void setModeleFichePaie(ModeleFichePaie modeleFichePaie) {
		this.modeleFichePaie = modeleFichePaie;
	}

	@Column(name = "EMP_NBJOURTRAVAIL")
	public Integer getNbJourTravail() {
		return nbJourTravail;
	}

	public void setNbJourTravail(Integer nbJourTravail) {
		this.nbJourTravail = nbJourTravail;
	}

	@JsonIgnore
	@OneToOne(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
	@JoinColumn(name = "CONTRAT_ID", foreignKey = @ForeignKey(name = "FK_EMPLOYE_CONTRAT_ID"))
	public Contrat getContratEncours() {
		return contratEncours;
	}

	public void setContratEncours(Contrat contratEncours) {
		this.contratEncours = contratEncours;
	}

	@Column(name = "EMP_ANNES_EXPERIENCE")
	public Integer getAnnesExperience() {
		return annesExperience;
	}

	public void setAnnesExperience(Integer annesExperience) {
		this.annesExperience = annesExperience;
	}

	@Column(name = "EMP_NOMBRE_POINTS")
	public Integer getNombrePoints() {
		return nombrePoints;
	}

	public void setNombrePoints(Integer nombrePoints) {
		this.nombrePoints = nombrePoints;
	}

	@Column(name = "EMP_PRET_OU_AVANCE")
	public Double getPretOuAvance() {
		return pretOuAvance;
	}

	public void setPretOuAvance(Double pretOuAvance) {
		this.pretOuAvance = pretOuAvance;
	}

	@Column(name = "EMP_NOMBRE_DOMESTIQUE")
	public Integer getNombreDomestique() {
		return nombreDomestique;
	}

	public void setNombreDomestique(Integer nombreDomestique) {
		this.nombreDomestique = nombreDomestique;
	}

	@Column(name = "EMP_STATUTMAT")
	public EStatutMatrimonial getStatutMatrimonial() {
		return statutMatrimonial;
	}

	public void setStatutMatrimonial(EStatutMatrimonial statutMatrimonial) {
		this.statutMatrimonial = statutMatrimonial;
	}

	@Column(name = "EMP_PHOTO")
	public byte[] getPhotoByteArray() {
		return photoByteArray;
	}

	public void setPhotoByteArray(byte[] photoByteArray) {
		this.photoByteArray = photoByteArray;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "MODEPAIEMENT_ID", foreignKey = @ForeignKey(name = "FK_EMPLOYE_MODEPAIEMT_ID"))
	public ModePaiement getModePaiement() {
		return modePaiement;
	}

	public void setModePaiement(ModePaiement modePaiement) {
		this.modePaiement = modePaiement;
	}

	@Column(name = "EMP_CODEBANQUECOMPTE")
	public String getCodeBanqueCompte() {
		return codeBanqueCompte;
	}

	public void setCodeBanqueCompte(String codeBanqueCompte) {
		this.codeBanqueCompte = codeBanqueCompte;
	}

	@Column(name = "EMP_CODEAGENCECOMPTE")
	public String getCodeAgenceCompte() {
		return codeAgenceCompte;
	}

	public void setCodeAgenceCompte(String codeAgenceCompte) {
		this.codeAgenceCompte = codeAgenceCompte;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "STATUTEMPLOYE_ID", foreignKey = @ForeignKey(name = "FK_STATUT_EMPLOYE_ID"))
	public StatutEmploye getStatutEmploye() {
		return statutEmploye;
	}

	public void setStatutEmploye(StatutEmploye statutEmploye) {
		this.statutEmploye = statutEmploye;
	}

	@Override
	public String toString() {
		return "Employe [matricule=" + matricule + "]";
	}

	@Override
	public int compareTo(Employe o) {
		if (matricule != null && o != null && o.matricule != null)
			return matricule.compareTo(o.matricule);
		return -1;
	}

	@OneToOne(cascade = CascadeType.ALL)
	@JoinColumn(name = "CASIER_JUDICIAIRE_ID", foreignKey = @ForeignKey(name = "FK_EMPLOYE_CASIER_JUDICIAIRE_ID"))
	public CasierJudiciaire getCasierJudiciaire() {
		return casierJudiciaire;
	}

	public void setCasierJudiciaire(CasierJudiciaire casierJudiciaire) {
		this.casierJudiciaire = casierJudiciaire;
	}

	public ENationalStatut getNationalStatus() {
		return nationalStatus;
	}

	public void setNationalStatus(ENationalStatut nationalStatus) {
		this.nationalStatus = nationalStatus;
	}

	public String getAutorisationNumber() {
		return autorisationNumber;
	}

	public void setAutorisationNumber(String autorisationNumber) {
		this.autorisationNumber = autorisationNumber;
	}

	@Column(name = "EMP_PERCENTAGE_MATCHING_FORMATIONS")
	public Double getPercentageMatchingFormations() {
		return percentageMatchingFormations;
	}

	public void setPercentageMatchingFormations(Double percentageMatchingFormations) {
		this.percentageMatchingFormations = percentageMatchingFormations;
	}

	public Boolean getPremierEmploie() {
		return premierEmploie;
	}

	public void setPremierEmploie(Boolean premierEmploie) {
		this.premierEmploie = premierEmploie;
	}

	@Embedded
	private Auditable auditable;

	public Auditable getAuditable() {
		return auditable;
	}

	public void setAuditable(Auditable auditable) {
		this.auditable = auditable;
	}

	@Enumerated(EnumType.STRING)
	public EStatutEmploye getStatut() {
		return statut;
	}

	public void setStatut(EStatutEmploye statut) {
		this.statut = statut;
	}

	@Column(name = "EMP_DATE_PRM_EMPL")
	@Temporal(TemporalType.DATE)
	public Date getDateInstaurationPremierEmploi() {
		return dateInstaurationPremierEmploi;
	}

	public void setDateInstaurationPremierEmploi(Date dateInstaurationPremierEmploi) {
		this.dateInstaurationPremierEmploi = dateInstaurationPremierEmploi;
	}

	public String getNumeroIdentificationUnique() {
		return numeroIdentificationUnique;
	}

	public void setNumeroIdentificationUnique(String numeroIdentificationUnique) {
		this.numeroIdentificationUnique = numeroIdentificationUnique;
	}

	public String toAuditString() {
		StringBuilder sb = new StringBuilder();

		sb.append("id=");
		sb.append(id);

		sb.append("#noms=");
		sb.append(noms);

		sb.append("#prenom=");
		sb.append(prenom);

		sb.append("#titre=");
		sb.append(titre);

		sb.append("#poste Responsabilite=");
		sb.append(posteResponsabilite == null ? null : posteResponsabilite.getIntitule());

		sb.append("#fonction=");
		sb.append(fonction == null ? null : fonction.getIntitule());

		sb.append("#cv=");
		sb.append(cv == null ? null : cv.getIntitule());

		sb.append("#date Confirmation=");
		sb.append(dateConfirmation);

		sb.append("#date Embauche=");
		sb.append(dateEmbauche);

		sb.append("#date Fin Embauche=");
		sb.append(dateFinEmbauche);

		sb.append("#motif Rup Contrat=");
		sb.append(motifRupContrat);

		sb.append("#classe=");
		sb.append(classe == null ? null : classe.getIntitule());

		sb.append("#grade=");
		sb.append(grade == null ? null : grade.getIntitule());

		sb.append("#unite=");
		sb.append(unite == null ? null : unite.getIntitule());

		sb.append("#agence=");
		sb.append(agence == null ? null : agence.getIntitule());

		sb.append("#agence Virement=");
		sb.append(agenceVirement == null ? null : agenceVirement.getIntitule());

		sb.append("#cellule=");
		sb.append(cellule == null ? null : cellule.getIntitule());

		sb.append("#user=");
		sb.append(user == null ? null : user.getIntitule());

		sb.append("#radie=");
		sb.append(radie);

		sb.append("#matricule Cnps=");
		sb.append(matriculeCnps);

		sb.append("#num Contribuable=");
		sb.append(numContribuable);

		sb.append("#matricule=");
		sb.append(matricule);

		sb.append("#numero Compte=");
		sb.append(numeroCompte);

		sb.append("#code Banque Compte=");
		sb.append(codeBanqueCompte);

		sb.append("#code Agence Compte=");
		sb.append(codeAgenceCompte);

		sb.append("#salaire Brut=");
		sb.append(salaireBrut);

		sb.append("#salaire Net=");
		sb.append(salaireNet);

		sb.append("#salaire Base=");
		sb.append(salaireBase);

		sb.append("#lieu Cnps=");
		sb.append(lieuCnps);

		sb.append("#employeur=");
		sb.append(employeur);

		sb.append("#dateCnps=");
		sb.append(dateCnps);

		sb.append("#contrat En cours=");
		sb.append(contratEncours == null ? null : contratEncours.getContrat().name());

		sb.append("#nombre enfants=");
		sb.append(enfants == null ? 0 : enfants.size());

		sb.append("#nombre managers=");
		sb.append(managers == null ? 0 : managers.size());

		sb.append("#nombre fonctions=");
		sb.append(fonctions == null ? 0 : fonctions.size());

		sb.append("#nombre diplomes=");
		sb.append(diplomes == null ? 0 : diplomes.size());

		sb.append("#nombre comptes=");
		sb.append(comptes == null ? 0 : comptes.size());

		sb.append("#enfant En charge=");
		sb.append(enfantEncharge);

		sb.append("#conjoint Salarie=");
		sb.append(conjointSalarie);

		sb.append("#nature=");
		sb.append(nature == null ? null : nature.name());

		sb.append("#titre Employe=");
		sb.append(titreEmploye == null ? null : titreEmploye.name());

		sb.append("#photoByte Array=");
		sb.append(photoByteArray == null ? null : Arrays.toString(photoByteArray));

		return sb.toString();

	}

}
