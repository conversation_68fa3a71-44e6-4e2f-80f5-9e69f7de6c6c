{"_from": "flatted@^3.2.9", "_id": "flatted@3.3.3", "_inBundle": false, "_integrity": "sha512-GX+ysw4PBCz0PzosHDepZGANEuFCMLrnRTiEy9McGjmkCQYwRq4A/X786G/fjM/+OjsWSU1ZrY5qyARZmO/uwg==", "_location": "/flatted", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "flatted@^3.2.9", "name": "flatted", "escapedName": "flatted", "rawSpec": "^3.2.9", "saveSpec": null, "fetchSpec": "^3.2.9"}, "_requiredBy": ["#USER", "/"], "_resolved": "https://registry.npmjs.org/flatted/-/flatted-3.3.3.tgz", "_shasum": "67c8fad95454a7c7abebf74bb78ee74a44023358", "_spec": "flatted@^3.2.9", "_where": "D:\\Dev projets\\PKF_Work\\EHR\\ehr_afb\\ehr-web", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/WebReflection/flatted/issues"}, "bundleDependencies": false, "deprecated": false, "description": "A super light and fast circular JSON parser.", "devDependencies": {"@babel/core": "^7.26.9", "@babel/preset-env": "^7.26.9", "@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-terser": "^0.4.4", "@ungap/structured-clone": "^1.3.0", "ascjs": "^6.0.3", "c8": "^10.1.3", "circular-json": "^0.5.9", "circular-json-es6": "^2.0.2", "jsan": "^3.1.14", "rollup": "^4.34.8", "terser": "^5.39.0", "typescript": "^5.7.3"}, "exports": {".": {"types": "./types/index.d.ts", "import": "./esm/index.js", "default": "./cjs/index.js"}, "./esm": "./esm.js", "./package.json": "./package.json"}, "files": ["LICENSE", "README.md", "cjs/", "es.js", "esm.js", "esm/", "index.js", "min.js", "php/flatted.php", "python/flatted.py", "types/"], "homepage": "https://github.com/WebReflection/flatted#readme", "keywords": ["circular", "JSON", "fast", "parser", "minimal"], "license": "ISC", "main": "./cjs/index.js", "module": "./esm/index.js", "name": "flatted", "repository": {"type": "git", "url": "git+https://github.com/WebReflection/flatted.git"}, "scripts": {"build": "npm run cjs && npm run rollup:esm && npm run rollup:es && npm run rollup:babel && npm run min && npm run test && npm run size", "cjs": "ascjs esm cjs", "coverage": "mkdir -p ./coverage; c8 report --reporter=text-lcov > ./coverage/lcov.info", "min": "terser index.js -c -m -o min.js", "rollup:babel": "rollup --config rollup/babel.config.js && sed -i.bck 's/^var /self./' index.js && rm -rf index.js.bck", "rollup:es": "rollup --config rollup/es.config.js && sed -i.bck 's/^var /self./' es.js && rm -rf es.js.bck", "rollup:esm": "rollup --config rollup/esm.config.js", "size": "cat index.js | wc -c;cat min.js | wc -c;gzip -c9 min.js | wc -c;cat min.js | brotli | wc -c; cat es.js | brotli | wc -c; cat esm.js | brotli | wc -c", "test": "c8 node test/index.js", "test:php": "php php/test.php", "test:py": "python python/test.py", "ts": "tsc -p ."}, "type": "module", "types": "./types/index.d.ts", "unpkg": "min.js", "version": "3.3.3"}