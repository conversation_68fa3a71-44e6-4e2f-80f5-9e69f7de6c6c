package org.pkfrc.ehr.entities.dashboard;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;

import org.pkfrc.core.entities.AbstractEntity;

import lombok.Data;

@Data
public class DashboardEmploye extends AbstractEntity<Long> {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private Double montantTotalPret;
	private Long nombreTotalPret;
	private Double montantTotalPretEncours;
	private Long nombreTotalPretEncours;
	private Double montantTotalPretRembourse;
	private Long nombreTotalPretRembourse;
	private Double montantTotalPretSuspendu;
	private Long nombreTotalPretSuspendu;
	private Double montantTotalPretRestant;
	private BigInteger nombreTotalPretRestant;
	private Long employeeTotalNoteDisciplinaire;
	private Long employeeTotalSanction;
	private List<Object[]> employeeTotalSanctionByTypeSanction = new ArrayList<>();
	private Long nombreDemissionEffectuer;
	private List<Object[]> monthlySalaryProgression = new ArrayList<>();
	private List<Object[]> employeeDocumentsByEtatDoc = new ArrayList<>();
	private Long noteDiscplinaireEnAttenteJustificatif;
	private Double percentageOfMismatchedFormations;
	private Double totalDaysRequestedByExerciceAndEmpMatricule;
	private Double totalDaysRequestedExcludingCongesAnnuel;
	private List<Object[]> employeeTotalNoteDisciplinaireByTypeNd = new ArrayList<>();
	private List<Object[]> employeeDocRealisationExercice = new ArrayList<>();
	private List<Object[]> employeeNbreJrsCongeByIntitule = new ArrayList<>();
	private int nombreCampagneRecrutement;

	public DashboardEmploye() {
		super();
	}

	@Override
	public Long getId() {
		// TODO Auto-generated method stub
		return id;
	}

	public Double getMontantTotalPret() {
		return montantTotalPret;
	}

	public void setMontantTotalPret(Double montantTotalPret) {
		this.montantTotalPret = montantTotalPret;
	}

	public Long getNombreTotalPret() {
		return nombreTotalPret;
	}

	public void setNombreTotalPret(Long nombreTotalPret) {
		this.nombreTotalPret = nombreTotalPret;
	}

	public Double getMontantTotalPretEncours() {
		return montantTotalPretEncours;
	}

	public void setMontantTotalPretEncours(Double montantTotalPretEncours) {
		this.montantTotalPretEncours = montantTotalPretEncours;
	}

	public Long getNombreTotalPretEncours() {
		return nombreTotalPretEncours;
	}

	public void setNombreTotalPretEncours(Long nombreTotalPretEncours) {
		this.nombreTotalPretEncours = nombreTotalPretEncours;
	}

	public Double getMontantTotalPretRembourse() {
		return montantTotalPretRembourse;
	}

	public void setMontantTotalPretRembourse(Double montantTotalPretRembourse) {
		this.montantTotalPretRembourse = montantTotalPretRembourse;
	}

	public Long getNombreTotalPretRembourse() {
		return nombreTotalPretRembourse;
	}

	public void setNombreTotalPretRembourse(Long nombreTotalPretRembourse) {
		this.nombreTotalPretRembourse = nombreTotalPretRembourse;
	}

	public Double getMontantTotalPretSuspendu() {
		return montantTotalPretSuspendu;
	}

	public void setMontantTotalPretSuspendu(Double montantTotalPretSuspendu) {
		this.montantTotalPretSuspendu = montantTotalPretSuspendu;
	}

	public Long getNombreTotalPretSuspendu() {
		return nombreTotalPretSuspendu;
	}

	public void setNombreTotalPretSuspendu(Long nombreTotalPretSuspendu) {
		this.nombreTotalPretSuspendu = nombreTotalPretSuspendu;
	}

	public Double getMontantTotalPretRestant() {
		return montantTotalPretRestant;
	}

	public void setMontantTotalPretRestant(Double montantTotalPretRestant) {
		this.montantTotalPretRestant = montantTotalPretRestant;
	}

	public BigInteger getNombreTotalPretRestant() {
		return nombreTotalPretRestant;
	}

	public void setNombreTotalPretRestant(BigInteger nombreTotalPretRestant) {
		this.nombreTotalPretRestant = nombreTotalPretRestant;
	}

	public Long getEmployeeTotalNoteDisciplinaire() {
		return employeeTotalNoteDisciplinaire;
	}

	public void setEmployeeTotalNoteDisciplinaire(Long employeeTotalNoteDisciplinaire) {
		this.employeeTotalNoteDisciplinaire = employeeTotalNoteDisciplinaire;
	}

	public List<Object[]> getEmployeeTotalSanctionByTypeSanction() {
		return employeeTotalSanctionByTypeSanction;
	}

	public void setEmployeeTotalSanctionByTypeSanction(List<Object[]> employeeTotalSanctionByTypeSanction) {
		this.employeeTotalSanctionByTypeSanction = employeeTotalSanctionByTypeSanction;
	}

	public Long getNombreDemissionEffectuer() {
		return nombreDemissionEffectuer;
	}

	public void setNombreDemissionEffectuer(Long nombreDemissionEffectuer) {
		this.nombreDemissionEffectuer = nombreDemissionEffectuer;
	}

	public List<Object[]> getMonthlySalaryProgression() {
		return monthlySalaryProgression;
	}

	public void setMonthlySalaryProgression(List<Object[]> monthlySalaryProgression) {
		this.monthlySalaryProgression = monthlySalaryProgression;
	}

	public List<Object[]> getEmployeeDocumentsByEtatDoc() {
		return employeeDocumentsByEtatDoc;
	}

	public void setEmployeeDocumentsByEtatDoc(List<Object[]> employeeDocumentsByEtatDoc) {
		this.employeeDocumentsByEtatDoc = employeeDocumentsByEtatDoc;
	}

	public Long getNoteDiscplinaireEnAttenteJustificatif() {
		return noteDiscplinaireEnAttenteJustificatif;
	}

	public void setNoteDiscplinaireEnAttenteJustificatif(Long noteDiscplinaireEnAttenteJustificatif) {
		this.noteDiscplinaireEnAttenteJustificatif = noteDiscplinaireEnAttenteJustificatif;
	}

	public Double getPercentageOfMismatchedFormations() {
		return percentageOfMismatchedFormations;
	}

	public void setPercentageOfMismatchedFormations(Double percentageOfMismatchedFormations) {
		this.percentageOfMismatchedFormations = percentageOfMismatchedFormations;
	}

	public Double getTotalDaysRequestedByExerciceAndEmpMatricule() {
		return totalDaysRequestedByExerciceAndEmpMatricule;
	}

	public void setTotalDaysRequestedByExerciceAndEmpMatricule(Double totalDaysRequestedByExerciceAndEmpMatricule) {
		this.totalDaysRequestedByExerciceAndEmpMatricule = totalDaysRequestedByExerciceAndEmpMatricule;
	}

	public Double getTotalDaysRequestedExcludingCongesAnnuel() {
		return totalDaysRequestedExcludingCongesAnnuel;
	}

	public void setTotalDaysRequestedExcludingCongesAnnuel(Double totalDaysRequestedExcludingCongesAnnuel) {
		this.totalDaysRequestedExcludingCongesAnnuel = totalDaysRequestedExcludingCongesAnnuel;
	}

	public List<Object[]> getEmployeeTotalNoteDisciplinaireByTypeNd() {
		return employeeTotalNoteDisciplinaireByTypeNd;
	}

	public void setEmployeeTotalNoteDisciplinaireByTypeNd(List<Object[]> employeeTotalNoteDisciplinaireByTypeNd) {
		this.employeeTotalNoteDisciplinaireByTypeNd = employeeTotalNoteDisciplinaireByTypeNd;
	}

	public List<Object[]> getEmployeeDocRealisationExercice() {
		return employeeDocRealisationExercice;
	}

	public void setEmployeeDocRealisationExercice(List<Object[]> employeeDocRealisationExercice) {
		this.employeeDocRealisationExercice = employeeDocRealisationExercice;
	}

	public List<Object[]> getEmployeeNbreJrsCongeByIntitule() {
		return employeeNbreJrsCongeByIntitule;
	}

	public void setEmployeeNbreJrsCongeByIntitule(List<Object[]> employeeNbreJrsCongeByIntitule) {
		this.employeeNbreJrsCongeByIntitule = employeeNbreJrsCongeByIntitule;
	}

	public Long getEmployeeTotalSanction() {
		return employeeTotalSanction;
	}

	public void setEmployeeTotalSanction(Long employeeTotalSanction) {
		this.employeeTotalSanction = employeeTotalSanction;
	}

	public int getNombreCampagneRecrutement() {
		return nombreCampagneRecrutement;
	}

	public void setNombreCampagneRecrutement(int nombreCampagneRecrutement) {
		this.nombreCampagneRecrutement = nombreCampagneRecrutement;
	}
}