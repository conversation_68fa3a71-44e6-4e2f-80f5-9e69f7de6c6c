package org.pkfrc.ehr.entities.dashboard;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;

import org.pkfrc.core.entities.AbstractEntity;

import lombok.Data;

@Data
public class DashboardEmploye extends AbstractEntity<Long> {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private Double montantTotalPret;
	private Long nombreTotalPret;
	private Double montantTotalPretEncours;
	private Long nombreTotalPretEncours;
	private Double montantTotalPretRembourse;
	private Long nombreTotalPretRembourse;
	private Double montantTotalPretSuspendu;
	private Long nombreTotalPretSuspendu;
	private Double montantTotalPretRestant;
	private BigInteger nombreTotalPretRestant;
	private Long employeeTotalNoteDisciplinaire;
	private Long employeeTotalSanction;
	private List<Object[]> employeeTotalSanctionByTypeSanction = new ArrayList<>();
	private Long nombreDemissionEffectuer;
	private List<Object[]> monthlySalaryProgression = new ArrayList<>();
	private List<Object[]> employeeDocumentsByEtatDoc = new ArrayList<>();
	private Long noteDiscplinaireEnAttenteJustificatif;
	private Double percentageOfMismatchedFormations;
	private Double totalDaysRequestedByExerciceAndEmpMatricule;
	private Double totalDaysRequestedExcludingCongesAnnuel;
	private List<Object[]> employeeTotalNoteDisciplinaireByTypeNd = new ArrayList<>();
	private List<Object[]> employeeDocRealisationExercice = new ArrayList<>();
	private List<Object[]> employeeNbreJrsCongeByIntitule = new ArrayList<>();
	private int nombreCampagneRecrutement;

	public DashboardEmploye() {
		super();
	}

	@Override
	public Long getId() {
		return id;
	}

}